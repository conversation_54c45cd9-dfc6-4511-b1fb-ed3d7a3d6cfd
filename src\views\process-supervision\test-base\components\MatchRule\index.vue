<template>
  <div class="h-full overflow-hidden flex flex-col">
    <div class="mb-3 flex items-end justify-between">
      <div class="mb-3 text-left text-base flex items-center">
        <div>匹配规则 当前选中：</div>
        <div class="text-sm text-text_color_regular whitespace-pre">{{ props.standardItemName }}</div>
      </div>
      <div class="flex items-center justify-end">
        <!-- <div class="flex items-center">
          <el-input
            v-model="state.keyword"
            v-trim
            placeholder="请输入"
            size="small"
            prefix-icon="el-icon-search"
            clearable
            class="mr-3"
            @keyup.enter="handleQuery()"
            @clear="handleQuery()"
          />
          <el-button type="primary" size="small" :disabled="btnDisabled" @click="handleQuery()">查询</el-button>
          <el-button size="small" :disabled="btnDisabled" @click="onReset()">重置</el-button>
        </div> -->
        <div>
          <el-button
            :disabled="btnDisabled"
            type="primary"
            size="small"
            @click="onAddRuleItem(true)"
            @keyup.prevent
            @keydown.enter.prevent
            >添加规则</el-button
          >
          <el-button
            type="primary"
            size="small"
            :disabled="btnDisabled"
            @click="onImportRuleDialogVisible()"
            @keyup.prevent
            @keydown.enter.prevent
            >规则导入</el-button
          >
        </div>
      </div>
    </div>
    <div class="flex-1 overflow-hidden">
      <el-table
        v-loading="state.loading"
        :data="state.rules"
        fit
        border
        height="auto"
        size="medium"
        highlight-current-row
        class="dark-table format-height-table base-table format-height-table2 overflow-auto !h-full"
        @header-dragend="drageHeader"
      >
        <el-table-column label="客户" width="150px" prop="customerCode" show-overflow-tooltip>
          <template #default="{ row }">
            <template v-if="!row.isAddOrEdit">{{ row.customerName }} </template>
            <el-select
              v-else
              v-model="row.customerCode"
              filterable
              placeholder="请选择客户"
              size="small"
              clearable
              @change="onChangeCustomerCode(row)"
            >
              <el-option
                v-for="(val, key) in state.dictionary['customer']?.enable"
                :key="key"
                :label="val"
                :value="key"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="项目名称" width="150px" prop="projectCode" show-overflow-tooltip>
          <template #default="{ row }">
            <template v-if="!row.isAddOrEdit">{{ row.projectName }} </template>
            <el-select
              v-else
              v-model="row.projectCode"
              filterable
              placeholder="请选择项目"
              size="small"
              clearable
              @change="onChangepPojectCode(row)"
            >
              <el-option
                v-for="(val, key) in state.dictionary['project']?.enable"
                :key="key"
                :label="val"
                :value="key"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="物料分组" prop="materialGroupName" show-overflow-tooltip>
          <template #default="{ $index, row }">
            <template v-if="!row.isAddOrEdit">{{ row.materialGroupName }} </template>

            <template v-else>
              <el-input
                v-model="row.materialGroupName"
                class="!cursor-pointer"
                readonly
                size="small"
                placeholder="请选择物料分组"
                @click="onMaterialgroupDialogVisible($index, row)"
              >
                <template #append>
                  <div class="text-center w-[30px] h-full" @click="onAddOrDeleteMaterialgroup($index, row)">
                    <el-icon>
                      <template v-if="row.materialGroupNo"><Close /></template>
                      <template v-else><Search /></template>
                    </el-icon>
                  </div>
                </template>
              </el-input>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="物料" prop="materialName" show-overflow-tooltip>
          <template #default="{ $index, row }">
            <template v-if="!row.isAddOrEdit">{{ row.materialName }} </template>
            <template v-else>
              <el-input
                v-model="row.materialName"
                class="!cursor-pointer"
                readonly
                size="small"
                placeholder="请选择物料"
                @click="onMaterialDialogVisible($index, row)"
              >
                <template #append>
                  <div class="text-center w-[30px] h-full" @click="onAddOrDeleteMaterialItem($index, row)">
                    <el-icon>
                      <template v-if="row.materialNo"><Close /></template>
                      <template v-else><Search /></template>
                    </el-icon>
                  </div>
                </template>
              </el-input>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="规格节点" width="100px" prop="type" show-overflow-tooltip>
          <template #default="{ row }"> {{ row.standardItemType }} </template>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="100px">
          <template #default="{ row }">{{ formatDate(row.updateTime) }} </template>
        </el-table-column>
        <el-table-column label="更新人" prop="updateBy" width="80px">
          <template #default="{ row }">
            <UserTag :name="getNameByid(row.updateBy) || '--'" />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100px">
          <template #default="{ $index, row }">
            <template v-if="!row.isAddOrEdit">
              <span class="blue-color" @click="onToggleEditModel(row)">编辑</span>
              <!-- <span class="blue-color">反查</span> -->
              <span class="blue-color" @click="onDelete(row.id)">删除</span>
            </template>
            <template v-else>
              <span class="blue-color" @click="onSave(row)">保存</span>
              <span class="blue-color" @click="onCancelSave($index, row)">取消</span>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <MaterialGroup
      :dialog-visiable="state.dialogMaterialgroupVisible"
      :detail-data="state.rowData"
      :is-add="false"
      @selectRow="onSelectMaterialgroup"
      @closeDialog="onCloseMaterialgroup"
    />
    <MaterialItem
      :dialog-visiable="state.dialogMaterialItemVisible"
      :detail-data="state.rowData"
      :is-add="false"
      @selectRow="onSelectMaterialItem"
      @closeDialog="onCloseMaterialItem"
    />
    <ImportRule
      :visible="state.importRuleDialogVisible"
      :standard-category-id="props.standardCategoryId"
      @close="handleCloseImport"
    />
  </div>
</template>
<script setup>
import { onMounted, reactive, watch } from 'vue';
import { drageHeader } from '@/utils/formatTable';
import UserTag from '@/components/UserTag';
import { getNameByid } from '@/utils/common';
import { getDictionary } from '@/api/user';
import {
  queryStandardMatchingParamsList,
  saveOrUpdateStandardMatching,
  deleteStandardMatchingParamsByIds
} from '@/api/testBase';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed } from 'vue';
import MaterialGroup from '@/components/BusinessComponents/MaterialGroup';
import MaterialItem from '@/components/BusinessComponents/MaterialItem';
import { Search, Close } from '@element-plus/icons';
import { formatDate } from '@/utils/formatTime';
import ImportRule from './ImportRule.vue';

const standardType = {
  /** 标准 */
  standard: 1,
  /** 型号 */
  model: 2,
  /** 显示产品 */
  modelProduct: 3
};
const standardTypeMapDesc = {
  [standardType.standard]: '标准分类节点',
  [standardType.model]: '型号节点',
  [standardType.modelProduct]: '标准节点'
};
const props = defineProps({
  standardItemId: {
    type: String,
    required: false,
    default: ''
  },
  standardCategoryId: {
    type: String,
    required: false,
    default: ''
  },
  standardItemTypeName: {
    type: String,
    required: false,
    default: ''
  },
  standardItemType: {
    type: Number,
    required: false,
    default: null
  },
  standardItemName: {
    type: String,
    required: false,
    default: ''
  },
  standardItemPath: {
    type: Array,
    required: false,
    default: () => []
  },
  batchSelectProductIds: {
    type: Array,
    required: false,
    default: () => []
  }
});
const state = reactive({
  keyword: null,
  loading: false,
  rules: [],
  dictionary: {
    project: {
      enable: {},
      all: {}
    },
    customer: {
      enable: {},
      all: {}
    }
  },
  dialogMaterialItemVisible: false,
  dialogMaterialgroupVisible: false,
  rowData: {},
  importRuleDialogVisible: false
});

let rowDataIndex = 0;
const btnDisabled = computed(() => props.standardItemId === 'all' || !props.standardItemId);

watch(
  () => props.standardItemId,
  standardItemId => {
    if (!standardItemId) {
      return;
    }
    handleQuery();
  }
);

onMounted(() => {
  getDictionaryList();
});

const onAddRuleItem = isAddOrEdit => {
  if (props.standardItemType === standardType.standard) {
    ElMessage.warning('标准分类不允许添加规则，可添加到型号和产品规格节点上');
    return;
  }

  if (
    props.standardItemType === standardType.modelProduct &&
    Array.isArray(props.batchSelectProductIds) &&
    props.batchSelectProductIds.length > 1
  ) {
    ElMessage.warning('一次仅支持添加一条产品规格的规则，如需一次添加多个可选择添加到型号级别');
    return;
  }

  state.rules.push({
    customerCode: '',
    projectCode: '',
    materialGroupNo: '',
    materialGroupName: '',
    materialNo: '',
    standardItemId: '',
    standardItemType: standardTypeMapDesc[props.standardItemType],
    standardItemName: '',
    isAddOrEdit: isAddOrEdit
  });
};

const onMaterialgroupDialogVisible = (index, rowData) => {
  state.dialogMaterialgroupVisible = true;
  rowDataIndex = index;
  state.rowData = rowData;
};

const onAddOrDeleteMaterialgroup = (index, rowData) => {
  if (rowData.materialGroupNo) {
    rowData.materialGroupNo = '';
    rowData.materialGroupName = '';
    rowData.materialName = '';
    rowData.materialNo = '';
  } else {
    onMaterialgroupDialogVisible(index, rowData);
  }
};

const onMaterialDialogVisible = (index, rowData) => {
  state.dialogMaterialItemVisible = true;
  rowDataIndex = index;
  state.rowData = rowData;
};

const onAddOrDeleteMaterialItem = (index, rowData) => {
  if (rowData.materialNo) {
    rowData.materialName = '';
    rowData.materialNo = '';
    if (!rowData.materialGroupName) {
      rowData.materialGroupNo = '';
    }
  } else {
    onMaterialDialogVisible(index, rowData);
  }
};

const onSelectMaterialgroup = value => {
  state.dialogMaterialgroupVisible = false;
  if (state.rowData.materialGroupNo && value.materialGroupNo !== state.rowData.materialGroupNo) {
    state.rules[rowDataIndex].materialName = '';
    state.rules[rowDataIndex].materialNo = '';
  }
  Object.assign(state.rules[rowDataIndex], value);
  state.rowData = state.rules[rowDataIndex];
};
const onCloseMaterialgroup = () => {
  state.dialogMaterialgroupVisible = false;
};

const onSelectMaterialItem = value => {
  state.dialogMaterialItemVisible = false;
  Object.assign(state.rules[rowDataIndex], value);
  state.rules[rowDataIndex].materialNo = value.materialCode;
  state.rowData = state.rules[rowDataIndex];
};

const onCloseMaterialItem = () => {
  state.dialogMaterialItemVisible = false;
};

const onToggleEditModel = rowData => {
  rowData.isAddOrEdit = true;
};

const onCancelSave = (index, rowData) => {
  if (rowData.id) {
    rowData.isAddOrEdit = false;
    return;
  }
  state.rules.splice(index, 1);
};

const onDelete = async id => {
  const confirmRes = await ElMessageBox.confirm('删除成功后，将无法撤回，是否确认删除？', '确认删除', {
    distinguishCancelAndClose: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消'
  }).catch(() => 'cancel');
  if (confirmRes === 'cancel') {
    return;
  }

  const { data } = await deleteStandardMatchingParamsByIds([id]);
  if (data.code !== 200) {
    ElMessage.warning('删除失败，请重试');
    return;
  }
  ElMessage.success('删除成功');
  handleQuery();
};
// const onReset = () => {
//   state.keyword = null;
//   handleQuery();
// };

const onSave = async data => {
  if (
    props.standardItemType === standardType.modelProduct &&
    Array.isArray(props.batchSelectProductIds) &&
    props.batchSelectProductIds.length > 1
  ) {
    ElMessage.warning('一次仅支持添加一条产品规格的规则，如需一次添加多个可选择添加到型号级别');
    return;
  }
  const { customerCode, projectCode, materialGroupNo, materialNo } = data;

  if (!props.standardItemId) {
    ElMessage.warning('请选择型号或产品规格');
    return;
  }

  if (!customerCode && !projectCode && !materialGroupNo && !materialNo) {
    ElMessage.warning('客户,项目,物料分组,物料,不能全部为空,请选择');
    return;
  }
  await saveOrUpdateStandardMatching({
    ...data,
    standardItemId: props.standardItemId,
    findPathList: props.standardItemPath
  });
  handleQuery();
  ElMessage.success('保存成功');
};

const getDictionaryList = () => {
  Object.keys(state.dictionary).forEach(async item => {
    const response = await getDictionary(item);
    if (response) {
      state.dictionary[item] = { enable: {}, all: {} };
      response.data.data.dictionaryoption.forEach(optionItem => {
        if (optionItem.status == 1) {
          state.dictionary[item].enable[optionItem.code] = optionItem.name;
        }
        state.dictionary[item].all[optionItem.code] = optionItem.name;
      });
    }
  });
};
const handleQuery = async () => {
  state.loading = true;
  const { data } = await queryStandardMatchingParamsList(props.standardItemId).finally(() => (state.loading = false));
  state.rules = data?.data || [];
};

const onChangeCustomerCode = rowData => {
  rowData.customerName = state.dictionary['customer']?.enable[rowData.customerCode];
};
const onChangepPojectCode = rowData => {
  rowData.projectName = state.dictionary['project']?.enable[rowData.projectCode];
};

const onImportRuleDialogVisible = () => {
  state.importRuleDialogVisible = true;
};

const handleCloseImport = isRefresh => {
  state.importRuleDialogVisible = false;
  if (isRefresh) {
    handleQuery();
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-table.format-height-table2) {
  .el-table__body-wrapper {
    // max-height: 250px;
    // overflow-y: auto;
    // overflow-x: hidden;
    overflow: auto;
  }
}
:deep(.el-input-group__append) {
  padding: 0 !important;
}
</style>
