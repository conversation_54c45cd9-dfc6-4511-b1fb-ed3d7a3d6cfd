<template>
  <div class="search-container">
    <div class="textbox-wrapper">
      <el-select
        id="querySelect"
        ref="queryRef"
        v-model="selectedFieldList"
        v-focus
        v-trim
        name="query-test"
        :placeholder="placeholder"
        style="width: 100%"
        size="large"
        multiple
        filterable
        clearable
        remote
        :remote-method="remoteMethod"
        :loading="loading"
        @clear="reset()"
        @remove-tag="removeTag"
        @focus="focusInput"
        @keyup.enter="enterQuery"
        @keyup.delete="deleteMethod"
      >
        <template #prefix>
          <span style="padding-left: 5px">
            <i class="el-icon-search" />
          </span>
        </template>
        <el-option
          v-for="item in generatedQueryList"
          :key="item.label"
          :label="item.label"
          :value="item.label"
          :disabled="item.disabled"
        >
          <!-- <span style="float: left">{{ item.name }}:{{ item.value }}</span> -->
        </el-option>
      </el-select>
    </div>
    <el-button class="query-btn" type="primary" size="small" @click="queryList()">查询</el-button>
    <el-button class="query-btn" size="small" @click="reset()">重置</el-button>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

export default {
  name: 'CombinationQuery',
  components: {},
  props: {
    fieldList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    fieldTip: {
      type: String,
      default: function () {
        return '内容';
      }
    }
  },
  emits: ['getSingleText', 'getParamList', 'resetSearch', 'getQueryInfo'],
  setup(props, context) {
    const route = useRoute();
    const queryRef = ref(null);
    const datas = reactive({
      selectedFieldList: [],
      generatedQueryList: [],
      loading: false,
      singleText: '',
      placeholder: `请输入${props.fieldTip}进行搜索或下拉选择进行组合查询`
    });
    watch(
      () => props.fieldTip,
      newValue => {
        datas.placeholder = `请输入${newValue}进行搜索或下拉选择进行组合查询`;
      }
    );
    // 查询
    const queryList = () => {
      if (datas.selectedFieldList.length > 0) {
        const singleIndex = datas.selectedFieldList.findIndex(item => {
          const itemArray = item.toString().split(':');
          return (
            props.fieldList.findIndex(item => item.name === itemArray[0] || item.fieldName === itemArray[0]) === -1
          );
        });
        if (singleIndex !== -1) {
          context.emit('getSingleText', datas.selectedFieldList[singleIndex].toString());
          context.emit('getQueryInfo', {
            param: datas.selectedFieldList[singleIndex].toString(),
            tableQueryParamList: [],
            selectedFieldList: datas.selectedFieldList
          });
          datas.selectedFieldList = [datas.selectedFieldList[singleIndex]];
        } else {
          const tableQueryParamList = [];
          let kyItemArray = '';
          datas.selectedFieldList.forEach(item => {
            kyItemArray = item.split(':');
            const fieldItem = props.fieldList.find(
              item => item.name === kyItemArray[0] || item.fieldName === kyItemArray[0]
            );
            tableQueryParamList.push({
              tableName: fieldItem.field || fieldItem.fieldKey,
              tableValue: kyItemArray.length === 2 ? kyItemArray[1] : item.slice(kyItemArray[0].length + 1)
            });
          });
          context.emit('getParamList', tableQueryParamList);
          context.emit('getQueryInfo', { param: '', tableQueryParamList, selectedFieldList: datas.selectedFieldList });
        }
      } else {
        setTimeout(() => {
          // console.log(queryRef.value.query)
          context.emit('getSingleText', datas.singleText);
          context.emit('getQueryInfo', {
            param: datas.singleText,
            tableQueryParamList: [],
            selectedFieldList: datas.selectedFieldList
          });
          if (datas.singleText) {
            datas.selectedFieldList.push(`${datas.singleText}`);
          }
        }, 100);
      }
    };

    const remoteMethod = queryText => {
      datas.singleText = queryText.trim();

      if (datas.singleText) {
        datas.loading = true;
        setTimeout(() => {
          datas.loading = false;
          datas.generatedQueryList = [
            {
              disabled: true,
              label: `${datas.singleText} (搜索${props.fieldTip}时, 可直接按Enter键或查询按钮进行搜索)`
            }
          ];
          datas.generatedQueryList = datas.generatedQueryList.concat(
            props.fieldList
              .filter(item => (item.isQuery !== undefined ? Boolean(item.isQuery) : item.isNotQuery !== 1))
              .map(item => {
                return {
                  disabled: false,
                  label: `${item.name || item.fieldName}:${datas.singleText}`
                };
              })
          );
        });
      } else {
        if (datas.selectedFieldList.length === 0) {
          reset();
        }
      }
    };

    const enterQuery = val => {
      if (datas.selectedFieldList.length === 0 && queryRef?.value.query) {
        const queryText = queryRef.value.query.toString().trim();
        datas.selectedFieldList.push(queryText);
      }
      if (queryRef.value) {
        queryRef.value.visible = false;
      }
      queryList();
    };

    const reset = () => {
      queryRef.value.query = '';
      datas.singleText = '';
      datas.selectedFieldList = [];
      datas.generatedQueryList = [];
      datas.placeholder = `请输入${props.fieldTip}进行搜索或下拉选择进行组合查询`;
      context.emit('resetSearch', true);
      // queryList()
    };

    const focusInput = val => {
      if (datas.selectedFieldList.length === 1) {
        const selectedStr = datas.selectedFieldList[0];
        const selectedArray = datas.selectedFieldList[0].toString().split(':');
        if (
          props.fieldList.findIndex(item => item.name === selectedArray[0] || item.fieldName === selectedArray[0]) ===
          -1
        ) {
          if (val && val.currentTarget) {
            datas.selectedFieldList = [];
            // datas.generatedQueryList = []
            setTimeout(() => {
              if (queryRef.value) {
                queryRef.value.query = selectedStr;
                datas.placeholder = '';
              }
            }, 10);
          }
        }
      }
    };

    const removeTag = val => {
      if (datas.selectedFieldList.length === 0) {
        reset();
      }
    };

    const deleteMethod = event => {
      if (event) {
        datas.singleText = event.target.value.trim();
      }
    };

    function consumeCurrentStatus() {
      const currentSorage = sessionStorage.getItem(route.name);
      if (currentSorage) {
        const currentState = JSON.parse(currentSorage);
        if (currentState?.selectedFieldList) {
          datas.generatedQueryList = [];
          currentState.selectedFieldList.forEach(item => {
            datas.generatedQueryList.push({
              label: item
            });
          });
          datas.selectedFieldList = [].concat(currentState.selectedFieldList);
        }
        sessionStorage.removeItem(route.name);
      }
    }

    consumeCurrentStatus();

    return {
      ...toRefs(datas),
      queryRef,
      queryList,
      remoteMethod,
      enterQuery,
      reset,
      focusInput,
      removeTag,
      deleteMethod
    };
  }
};
</script>
<style lang="scss" scoped>
.search-container {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex: 1 1 5rem;
  .textbox-wrapper {
    flex: 1;
    width: 100%;
  }
  .switch-wrapper {
    width: 10rem;
    margin-right: 0.5rem;
  }
  .default-search-wrapper {
    flex: 5;
  }
  .combination-wrapper {
    flex: 5;
    width: 100%;
    display: flex;
    flex-direction: row;
    .field-input,
    .value-input {
      width: 10rem;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .add-wrapper {
      width: 5rem;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .combination-search-wrapper {
      flex: 5;
    }
  }
  .query-btn {
    width: 5rem;
    margin-left: 0.5rem;
  }
}

.combination-search-wrapper.select-items {
  border: 0.071429rem solid #fff;
  background-color: #fff;
  box-shadow: 0 0 0.857143rem rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  // padding: 0.714286rem 0.714286rem 0;
  max-height: 8.571429rem;
  overflow-y: auto;
  display: flex;
  flex-direction: row;
  align-items: center;
  .el-tag {
    margin: 0;
  }
  .tag-wrapper {
    width: 100%;
    overflow-y: auto;
  }
}
</style>
