<template>
  <!-- 数据采集列表 -->
  <ListLayout :has-button-group="false" :has-search-panel="false" :has-quick-query="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="listQuery" size="large" class="sample-order-form" @submit.prevent>
        <el-form-item prop="param">
          <el-input
            v-model="listQuery.param"
            v-trim
            v-focus
            size="medium"
            placeholder="请输入设备编号/设备名称"
            class="ipt-360"
            prefix-icon="el-icon-search"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </template>
    <div class="datacollection-list">
      <el-row v-if="tableList.length !== 0" :gutter="24">
        <el-col v-for="(item, index) in tableList" :key="index" :span="6">
          <div class="grid-content collection-content">
            <div class="content-bd">
              <div class="item-r">
                <div class="item-title" :class="statusList[item.status]">
                  <span class="iconfont tes-Vector1 icon-status" />
                  <span class="title-c">{{ item.deviceNumber }}</span>
                  <el-tag v-if="item.status === 'Standby'" size="small" class="title-status" type="warning"
                    >待机</el-tag
                  >
                  <el-tag v-if="item.status === 'Running'" size="small" class="title-status" type="success"
                    >运行</el-tag
                  >
                  <el-tag v-if="item.status === 'Maintenance'" size="small" class="title-status">检修</el-tag>
                  <el-tag v-if="item.status === 'Fault'" size="small" class="title-status" type="danger">故障</el-tag>
                  <!-- <el-tag v-if="item.status==='Scrapped'" size="small" class="title-status" type="info">报废</el-tag> -->
                </div>
                <div class="item-txt"><span class="label-txt">设备名称：</span>{{ item.name }}</div>
                <div v-if="item.isEquipmentMetering" class="item-txt">
                  <span class="label-txt">检定有效期：</span
                  ><span v-if="item.validBeginDate !== ''"
                    >{{ formatDate(item.validBeginDate) }} <span class="label-space">~</span>
                    {{ formatDate(item.validEndDate) }}</span
                  >
                  <span v-else> - </span>
                </div>
              </div>
            </div>
            <div class="content-ft">
              <el-row>
                <el-col :span="11">
                  <el-popover placement="bottom" :width="300" trigger="click" @show="showDetail(item)">
                    <template #reference>
                      <el-button class="bg-btn btn-hover" size="small">
                        <i class="el-icon-tickets" @keyup.prevent @keydown.enter.prevent />项目详情</el-button
                      >
                    </template>
                    <div v-loading="loading" class="pop-box">
                      <div class="pop-title">数采项目：</div>
                      <div v-if="detailData.length !== 0">
                        <span v-for="(it, itindex) in detailData" :key="itindex">
                          {{ it.name }}
                          <i v-if="itindex !== detailData.length - 1">，</i>
                        </span>
                      </div>
                      <div v-else>-</div>
                    </div>
                  </el-popover>
                </el-col>
                <el-col :span="2">
                  <el-divider direction="vertical" />
                </el-col>
                <el-col :span="11">
                  <el-button class="bg-btn btn-hover" size="small" @click="goDetail(item)">
                    <i class="el-icon-pie-chart" /> 数据采集</el-button
                  >
                </el-col>
              </el-row>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-empty v-else class="empty" :image="emptyImg" description="暂无数据" />
    </div>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs } from 'vue';
import router from '@/router/index.js';

import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { acquisitionList, findCapabilityByDeviceId } from '@/api/datacollection';
import emptyImg from '@/assets/img/empty-data.png';

export default {
  name: 'DataCollection',
  components: { ListLayout },
  setup() {
    // const _ = inject('_')
    const state = reactive({
      tableList: [],
      loading: false,
      detailData: [],
      statusList: {
        Standby: 'warning',
        Running: 'success',
        Maintenance: '',
        Fault: 'danger',
        Scrapped: 'info'
      }
    });
    const listLoading = ref(false);
    const listQuery = reactive({
      page: 1,
      limit: -1,
      param: ''
    });
    // 处理listQuery数据
    const getList = query => {
      if (query) {
        listQuery.page = query.page;
        listQuery.limit = query.limit;
      }
      listLoading.value = false;
      const searchdata = JSON.parse(JSON.stringify(listQuery));
      searchdata.page = JSON.stringify(searchdata.page);
      searchdata.limit = JSON.stringify(searchdata.limit);
      acquisitionList(searchdata).then(res => {
        if (res.data.code === 200) {
          state.tableList = [];
          res.data.data.list.forEach(item => {
            if (item.status !== 'Scrapped') {
              state.tableList.push(item);
            }
          });
        }
      });
    };
    getList();

    function onSubmit() {
      getList();
    }

    function reset() {
      listQuery.page = 1;
      listQuery.limit = -1;
      listQuery.param = '';
      getList();
    }

    const showDetail = item => {
      // console.log(item)
      state.loading = true;
      findCapabilityByDeviceId(item.id).then(res => {
        state.detailData = res.data.data;
        state.loading = false;
      });
    };

    const search = () => {};
    // 跳转数采详情
    const goDetail = row => {
      router.push({
        path: '/collectiondetail',
        query: {
          id: row.id,
          deviceNumber: row.deviceNumber,
          name: row.name
        }
      });
    };
    return {
      ...toRefs(state),
      emptyImg,
      showDetail,
      goDetail,
      getPermissionBtn,
      drageHeader,
      getNameByid,
      formatDate,
      getList,
      listLoading,
      listQuery,
      search,
      onSubmit,
      reset
    };
  },
  computed: {},
  created() {}
};
</script>
<style scoped lang="scss">
.datacollection {
  padding: 16px 24px 0;
  box-sizing: border-box;

  .sample-order-form {
    text-align: left;
  }
}

.datacollection-list {
  overflow-y: auto;
  overflow-x: hidden;
  padding-top: 4px;
  .el-row {
    margin-bottom: 20px;
    margin-right: 0 !important;
    margin-left: 0 !important;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.collection-content {
  background: $background-color;
  border-radius: 8px;
  margin-bottom: 24px;
  padding: 16px 20px 4px;
  box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.12);
  .content-ft {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 40px;
    .el-row {
      align-items: center;
    }
    .el-col {
      display: flex;
      &:first-of-type {
        justify-content: flex-end;
      }
      &:last-of-type {
        justify-content: flex-start;
      }
    }
  }

  .content-bd {
    overflow: hidden;
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 10px;
    .item-r {
      font-size: 14px;
      .item-title.info {
        .icon-status {
          background: $tes-grey;
        }
      }
      .item-title.success {
        .icon-status {
          background: $tes-green;
        }
      }
      .item-title.warning {
        .icon-status {
          background: $tes-yellow;
        }
      }
      .item-title.danger {
        .icon-status {
          background: $tes-red;
        }
      }
      .item-title {
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        justify-items: center;
        align-items: center;

        & > span {
          display: inline-block;
        }
        .title-c {
          font-size: 18px;
          color: #303133;
          overflow: hidden;
          font-weight: bold;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left;
          flex: auto;
        }
        .icon-status {
          width: 20px;
          height: 20px;
          border-radius: 2px;
          font-size: 12px;
          margin-right: 10px;
          background: $tes-primary;
          color: #fff;
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
      .item-txt {
        width: 100%;
        margin-top: 6px;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
        display: block;
        overflow: hidden;
        color: #303133;
        .label-txt {
          display: inline-block;
          width: 88px;
          color: #909399;
        }
        .label-space {
          color: #909399;
        }
      }

      & > div {
        line-height: 1.4;
        text-align: left;
        color: #606266;
      }
    }
  }
}
.btn-right {
  border-radius: 8px;
}
.btn-hover {
  cursor: pointer;
  font-size: 14px;
  i {
    margin-right: 2px;
  }
}
.btn-hover:hover,
.btn-hover:focus {
  background-color: #fff !important;
  color: $tes-primary;
}
.empty {
  margin-top: 15vh;
}
.bg-btn {
  padding: 0;
  font-weight: normal;
  border: none;
}

.pop-box {
  padding: 10px;
  .pop-title {
    color: #909399;
    margin-bottom: 4px;
  }
}

:deep(.page-list-main .el-container .el-main) {
  margin: 0 14px;
  background: transparent;
}

:deep(.el-container .el-main .page-main) {
  padding: 0;
  background: transparent;
}
</style>
