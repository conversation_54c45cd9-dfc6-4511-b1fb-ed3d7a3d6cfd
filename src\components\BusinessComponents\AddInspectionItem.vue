<template>
  <!-- 复杂弹窗 -->
  <el-dialog
    v-model="showDialog"
    custom-class="custom-dialog"
    title="添加项目"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterText"
          v-trim
          class="search"
          size="small"
          placeholder="请输入项目名称"
          prefix-icon="el-icon-search"
          maxlength="100"
          clearable
          @clear="filterClear()"
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
      <div class="header-right">
        <el-button size="small" @click="selectNone(newTreeDetail)" @keyup.prevent @keydown.enter.prevent
          >反选</el-button
        >
        <el-button size="small" @click="selectAll(treeDetail)" @keyup.prevent @keydown.enter.prevent>全选</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="6">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="treeRef"
                :data="treeData"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-loading="loading" class="list-container">
            <el-row
              v-for="(item, index) in newTreeDetail"
              :key="index"
              class="item-content"
              style="width: 100%"
              @click="changeCheckBox(item)"
            >
              <el-col :span="1">
                <div class="left">
                  <el-checkbox v-model="item.checked" :disabled="item.disabled" @change="changeCheckBox(item, 1)" />
                </div>
              </el-col>
              <el-col :span="22">
                <div class="main">
                  <div class="title" :class="{ 'title-checked': item.checked }">{{ item.name }}</div>
                  <div class="item-list">
                    <el-tag
                      v-for="(list, index1) in item.capabilityparaVoList"
                      :key="index1"
                      :type="item.checked ? 'primary' : 'info'"
                      >{{ list.name }}</el-tag
                    >
                  </div>
                </div>
              </el-col>
              <el-col :span="1">
                <div style="float: right">
                  <i
                    v-if="item.templateVersion"
                    style="color: green"
                    class="el-icon--right el-icon-document-checked"
                  /><i v-else style="color: red" class="el-icon--right el-icon-document-delete" />
                </div>
              </el-col>
            </el-row>
            <el-empty v-if="newTreeDetail.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-other">
      <div class="title">
        <label>已选项目</label>
        <el-button v-if="tags.length > 0" size="small" icon="el-icon-delete" @click="clear">清空</el-button>
      </div>
      <div v-if="oldTags.length > 0 || tags.length > 0" class="select-items">
        <el-tag
          v-for="tag in oldTags"
          :key="tag.name || tag.sourceName || tag.capabilityName"
          :closable="tag.closable"
          size="small"
          @close="closeTag(tag)"
        >
          {{ tag.name || tag.sourceName || tag.capabilityName }}
        </el-tag>
        <el-tag
          v-for="tag in tags"
          :key="tag.name || tag.sourceName || tag.capabilityName"
          closable
          size="small"
          @close="closeTag(tag)"
        >
          {{ tag.name || tag.sourceName || tag.capabilityName }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, inject, nextTick } from 'vue';
import { getCapabilityUplist } from '@/api/user';
import { formatTree } from '@/utils/formatJson';
import { getCapabilityTree } from '@/api/user';
import _ from 'lodash';
import emptyImg from '@/assets/img/empty-data.png';

export default {
  name: 'AddInspectionItem',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    materialCategoryCode: {
      type: String,
      default: ''
    },
    allocation: {
      type: Boolean,
      default: function () {
        return false;
      }
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const lodash = inject('_');
    const datas = reactive({
      showDialog: props.show,
      inputRef: ref(),
      filterText: '',
      treeRef: ref(),
      pageType: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tags: [],
      materialCode: '',
      oldTags: [],
      treeData: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      isRefresh: true
    });

    watch(props, newValue => {
      datas.showDialog = newValue.show;
      if (datas.showDialog) {
        datas.filterText = '';
        datas.pageType = newValue.type;
        datas.materialCode = newValue.materialCategoryCode;
        getLeftTree();
        datas.isRefresh = false;
        nextTick(() => {
          datas.inputRef.focus();
        });
        datas.tags = [];
        datas.oldTags = JSON.parse(JSON.stringify(newValue.data));
      }
      if (datas.isRefresh === false) {
        return false;
      }
    });
    const getLeftTree = () => {
      getCapabilityTree(datas.materialCode).then(res => {
        if (res) {
          const data = res.data.data;
          if (data.length) {
            datas.treeData = formatTree(data);
            datas.treeData.unshift({
              id: '-1',
              parentId: '0',
              materialCategoryCode: data.length > 0 ? data[0].materialCategoryCode : '266013',
              name: '全部',
              order: 0,
              status: 2
            });
            nextTick(() => {
              datas.treeRef.setCurrentKey('-1', true);
            });
            proxy.getCapabilityList(datas.treeData[0].id, datas.materialCode);
          } else {
            datas.treeData = [];
          }
        }
      });
    };

    // 过滤树节点
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      proxy.getCapabilityList(data.id, data.materialCategoryCode);
    };

    // 确定选择
    const dialogSuccess = () => {
      context.emit('selectData', datas.tags);
      datas.showDialog = false;
      datas.isRefresh = true;
      context.emit('close', false);
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      datas.isRefresh = true;
      context.emit('close', false);
    };
    // 全选
    const selectAll = arr => {
      if (arr && arr.length > 0) {
        datas.tags = [];
        arr.forEach(item => {
          const hasitem = _.filter(datas.oldTags, res => {
            res.closable = false;
            return res.id === item.id || item.id === res.sourceid || res.capabilityId === item.id;
          });
          if (hasitem.length > 0) {
            item.checked = true;
            item.disabled = true;
          } else {
            item.checked = true;
            filterItem(item);
          }
          // datas.tags.push(item)
        });
      }
    };
    // 反选
    const selectNone = arr => {
      if (arr && arr.length > 0) {
        arr.forEach(item => {
          if (!item.disabled) {
            if (item.checked) {
              item.checked = false;
              datas.tags = datas.tags.filter(val => {
                return val.name !== item.name;
              });
            } else {
              item.checked = true;
              filterItem(item);
            }
          }
        });
      }
    };
    // changeCheckBox
    const changeCheckBox = (item, flag) => {
      if (item.disabled) {
        return false;
      }
      item.checked = !item.checked;
      if (item.checked) {
        filterItem(item);
      } else {
        lodash.remove(datas.tags, n => {
          return item.id === n.id;
        });
      }
    };
    // 数据过滤
    const filterItem = item => {
      item.sourcecapabilitytype = 'Internal';
      item.newcapabilityparaVoList = [];
      if (item.capabilityparaVoList && item.capabilityparaVoList.length > 0) {
        item.capabilityparaVoList.forEach(cap => {
          const newItem1 = props.allocation
            ? {
                sourceIdStr: cap.id,
                sourcenumber: cap.number,
                sourceparentIdStr: cap.capabilityid,
                sourcecapabilitydescription: cap.description,
                sourceName: cap.name,
                sourceCapabilityType: cap.sourcecapabilitytype,
                experimentCategoryIdStr: cap.categoryid
              }
            : {
                sourceIdStr: cap.id,
                sourcenumber: cap.number,
                sourceparentIdStr: cap.capabilityid,
                sourcecapabilitydescription: cap.description,
                sourcename: cap.name,
                sourcecapabilitytype: cap.sourcecapabilitytype,
                experimentCategoryIdStr: cap.categoryid
              };
          item.newcapabilityparaVoList.push(newItem1);
        });
      }
      const newItem = props.allocation
        ? {
            sourceIdStr: item.id,
            sourcenumber: item.number,
            sourceparentIdStr: item.capabilityid,
            sourcecapabilitydescription: item.description,
            sourceName: item.name,
            sourceCapabilityType: item.sourcecapabilitytype,
            experimentCategoryIdStr: item.categoryid,
            parentid: '0',
            operationType: 1,
            status: 1,
            childList: item.newcapabilityparaVoList
          }
        : {
            sourceIdStr: item.id,
            sourcenumber: item.number,
            sourceparentIdStr: item.capabilityid,
            sourcecapabilitydescription: item.description,
            sourcename: item.name,
            sourcecapabilitytype: item.sourcecapabilitytype,
            experimentCategoryIdStr: item.categoryid,
            parentid: '0',
            operationType: 1,
            status: 1,
            childList: item.newcapabilityparaVoList
          };
      const param = Object.assign(item, newItem);
      datas.tags.push(param);
    };
    // 关闭tags
    const closeTag = tag => {
      datas.tags.splice(datas.tags.indexOf(tag), 1);
      datas.newTreeDetail.forEach(item => {
        const hasitem = _.filter(datas.oldTags, res => {
          res.closable = false;
          return res.id === item.id || item.id === res.sourceid || res.capabilityId === item.id;
        });
        // 判断是否已经勾选过但未曾确认选择
        const hasitem2 = _.filter(datas.tags, res => {
          res.closable = false;
          return res.id === item.id || item.id === res.sourceid || res.capabilityId === item.id;
        });
        if (hasitem.length > 0) {
          item.checked = true;
          item.disabled = true;
        } else {
          if (hasitem2.length > 0) {
            item.checked = true;
          } else {
            item.checked = false;
          }
        }
      });
    };
    // 清空
    const clear = () => {
      datas.tags = [];
      if (datas.newTreeDetail && datas.newTreeDetail.length > 0) {
        datas.newTreeDetail.forEach(list => {
          const hasitem = _.filter(datas.oldTags, res => {
            res.closable = false;
            return res.id === list.id || list.id === res.sourceid || res.capabilityId === list.id;
          });
          if (hasitem.length > 0) {
            list.checked = true;
            list.disabled = true;
          } else {
            list.checked = false;
            datas.tags = [];
          }
        });
      }
    };
    // searchItem
    const searchItem = value => {
      if (value) {
        datas.newTreeDetail = [];
        datas.newTreeDetail = datas.newTreeDetail.concat(
          datas.treeDetail.filter(item => {
            return JSON.stringify(item).indexOf(value) !== -1;
          })
        );
      } else {
        datas.newTreeDetail = datas.treeDetail;
      }
    };

    const filterClear = () => {
      datas.newTreeDetail = datas.treeDetail;
    };

    return {
      ...toRefs(datas),
      emptyImg,
      searchItem,
      dialogSuccess,
      close,
      filterNode,
      selectAll,
      selectNone,
      clickNode,
      closeTag,
      clear,
      changeCheckBox,
      filterItem,
      filterClear
    };
  },
  methods: {
    getCapabilityList(id, materialCategoryCode) {
      // 获取检测项目list
      const vm = this;
      vm.loading = true;
      getCapabilityUplist(id, materialCategoryCode).then(response => {
        vm.loading = false;
        if (response !== false && response.data.code === 200) {
          const { data } = response.data;
          vm.treeDetail = [];
          if (vm.pageType === 'testBase') {
            data.forEach(item => {
              const newChild = [];
              item.capabilityparaVoList.forEach(val => {
                if (val.applylabel.includes('2')) {
                  newChild.push(val);
                }
              });
              vm.treeDetail.push({ ...item, capabilityparaVoList: newChild });
            });
          } else {
            vm.treeDetail = data;
          }
          if (vm.filterText && vm.treeDetail.length > 0) {
            vm.newTreeDetail = vm.treeDetail.filter(item => {
              return JSON.stringify(item).indexOf(vm.filterText) !== -1;
            });
          } else {
            vm.newTreeDetail = vm.treeDetail;
          }
          vm.newTreeDetail.forEach(item => {
            const hasitem = _.filter(vm.oldTags, res => {
              res.closable = false;
              return res.id === item.id || item.id === res.sourceId || res.capabilityId === item.id;
            });
            // 判断是否已经勾选过但未曾确认选择
            const hasitem2 = _.filter(vm.tags, res => {
              res.closable = false;
              return res.id === item.id || item.id === res.sourceId || res.capabilityId === item.id;
            });
            if (hasitem.length > 0) {
              item.checked = true;
              item.disabled = true;
            } else {
              if (hasitem2.length > 0) {
                item.checked = true;
              } else {
                item.checked = false;
              }
            }
          });
        }
      });
    }
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  .tree-container {
    .tree-content {
      height: calc(100vh - 540px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 500px);
    overflow-y: auto;
  }
}
</style>
