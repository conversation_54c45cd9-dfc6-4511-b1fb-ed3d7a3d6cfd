<template>
  <div v-loading="pageLoading" element-loading-text="模板生成中..." class="h-full w-full overflow-hidden">
    <OnlineExcelBatchPrint ref="onlineExcelBatchPrint" @print-success="printSuccess" />
  </div>
</template>

<script setup>
import OnlineExcelBatchPrint from '@/components/online-excel-batch-print/index.vue';
import { getRawCertificatePrintData, updatePrintStatus } from '@/api/raw-certificate';
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const state = reactive({
  ids: route.query.ids,
  certificatePrintIds: route.query.certificatePrintIds,
  templateUrl: route.query.url,
  smallSerialNumber: route.query.smallSerialNumber,
  isShowCheckItem: route.query.isShowCheckItem,
  printData: []
});

const pageLoading = ref(true);

const onlineExcelBatchPrint = ref(null);

const getPrintData = async () => {
  const data = {
    samplesIds: JSON.parse(state.ids),
    certificatePrintIds: state.certificatePrintIds ? JSON.parse(state.certificatePrintIds) : [],
    smallSerialNumber: state.smallSerialNumber ? JSON.parse(state.smallSerialNumber) : [],
    isShowCheckItem: state.isShowCheckItem,
    templateUrl: state.templateUrl
  };
  const res = await getRawCertificatePrintData(data);
  state.printData = res.data.data;
};

const printSuccess = () => {
  updatePrintStatus(state.ids);
};

onMounted(async () => {
  await getPrintData();
  await onlineExcelBatchPrint.value.loadExcelTemplate(state.templateUrl);
  await onlineExcelBatchPrint.value.loadPreviewData(state.printData);
  await onlineExcelBatchPrint.value.loadPreviewImage();
  pageLoading.value = false;
});
</script>

<style lang="scss" scoped></style>
