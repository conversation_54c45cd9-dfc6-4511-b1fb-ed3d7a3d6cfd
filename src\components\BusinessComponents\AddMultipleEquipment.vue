<template>
  <!-- 选择仪器设备多选 -->
  <el-dialog
    v-model="showDialog"
    custom-class="custom-dialog"
    title="选择仪器设备"
    width="60%"
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterText"
          v-trim
          class="search"
          size="small"
          placeholder="请输入设备名称"
          prefix-icon="el-icon-search"
          maxlength="100"
          clearable
          @clear="filterClear()"
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
      <div class="header-right">
        <el-button size="small" @click="selectNone(newTreeDetail)" @keyup.prevent @keydown.enter.prevent
          >反选</el-button
        >
        <el-button size="small" @click="selectAll(treeDetail)" @keyup.prevent @keydown.enter.prevent>全选</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="6">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="leftTreeRef"
                :data="treeData"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-loading="loading" class="list-container">
            <el-row
              v-for="(item, index) in newTreeDetail"
              :key="index"
              class="item-content"
              style="width: 100%"
              @click="changeCheckBox(item)"
            >
              <el-col :span="1">
                <div class="left">
                  <el-checkbox v-model="item.checked" :disabled="item.disabled" @change="changeCheckBox(item, 1)" />
                </div>
              </el-col>
              <el-col :span="23">
                <div class="main">
                  <div class="title" :class="{ 'title-checked': item.checked }">
                    {{ item.name }}（{{ item.deviceNumber }}）
                  </div>
                  <!-- <el-tooltip
                    effect="light"
                    :content="`${item.name}(${item.deviceNumber || '--'})`"
                    placement="top-start"
                  >
                  </el-tooltip> -->
                </div>
              </el-col>
            </el-row>
            <el-empty v-if="newTreeDetail.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-other">
      <div class="title">
        <label>已选设备</label>
        <el-button v-if="tags.length > 0" size="small" icon="el-icon-delete" @click="clear">清空</el-button>
      </div>
      <div v-if="oldTags.length > 0 || tags.length > 0" class="select-items">
        <el-tag
          v-for="tag in oldTags"
          :key="tag.deviceNumber"
          :closable="tag.closable"
          size="small"
          @close="closeTag(tag)"
        >
          {{ tag.deviceName || tag.name }}（{{ tag.deviceNumber }}）
        </el-tag>
        <el-tag v-for="tag in tags" :key="tag.deviceNumber" size="small" closable @close="closeTag(tag)">
          {{ tag.deviceName || tag.name }}（{{ tag.deviceNumber }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess()">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, nextTick, inject } from 'vue';
import _ from 'lodash';
import { formatTree } from '@/utils/formatJson';
import { getTree, getList } from '@/api/equipment';
import emptyImg from '@/assets/img/empty-data.png';

export default {
  name: 'AddMultipleEquipment',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    selectInfo: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['selectData', 'close'],
  setup(props, context) {
    const lodash = inject('_');
    const state = reactive({
      inputRef: ref(),
      leftTreeRef: ref(),
      materialCode: '',
      defaultProps: {
        children: 'subList',
        label: 'name'
      },
      filterText: '',
      tags: [],
      showDialog: false,
      oldTags: [],
      treeData: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      selectedItemId: ''
    });

    watch(props, newValue => {
      if (newValue.show) {
        state.filterText = '';
        state.showDialog = newValue.show;
        getLeftTree();
        nextTick(() => {
          state.inputRef.focus();
        });
        state.tags = [];
        state.oldTags = JSON.parse(JSON.stringify(newValue.selectInfo));
        if (newValue.show && newValue.tree && newValue.tree.length > 0) {
          getEquipmentList(newValue.tree[0].id);
        }
      }
    });
    // 初始化树节点
    const getLeftTree = () => {
      getTree().then(res => {
        const treeData = formatTree(res.data.data);
        if (treeData.length > 0) {
          state.treeData = JSON.parse(JSON.stringify(treeData));
          const all = { id: '-1', name: '全部' };
          state.treeData.unshift(all);
          nextTick(() => {
            state.leftTreeRef.setCurrentKey('-1', true);
          });
          clickNode(state.treeData[0]);
        } else {
          state.treeData = [];
          state.newTreeDetail = [];
        }
      });
    };
    // 过滤树节点
    const treeRef = ref(null);
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = data => {
      getEquipmentList(data.id);
    };

    // 确定选择
    const dialogSuccess = () => {
      context.emit('selectData', state.tags);
      state.showDialog = false;
    };
    // 取消选择
    const close = () => {
      context.emit('close', true);
      state.showDialog = false;
    };

    const changeCheckBox = (item, flag) => {
      if (item.disabled) {
        return false;
      }
      item.checked = !item.checked;
      if (item.checked) {
        state.tags.push(item);
        // filterItem(item)
      } else {
        lodash.remove(state.tags, n => {
          return item.deviceNumber === n.deviceNumber;
        });
      }
    };
    // 关闭tags
    const closeTag = tag => {
      state.tags.splice(state.tags.indexOf(tag), 1);
      state.newTreeDetail.forEach(item => {
        const hasitem = _.filter(state.oldTags, res => {
          res.closable = false;
          return res.deviceNumber === item.deviceNumber;
        });
        // 判断是否已经勾选过但未曾确认选择
        const hasitem2 = _.filter(state.tags, res => {
          res.closable = false;
          return res.deviceNumber === item.deviceNumber;
        });
        if (hasitem.length === 1) {
          item.checked = true;
          item.disabled = true;
        } else {
          if (hasitem2.length === 1) {
            item.checked = true;
          } else {
            item.checked = false;
          }
        }
      });
    };
    // // 清空
    const clear = () => {
      state.tags = [];
      if (state.newTreeDetail && state.newTreeDetail.length > 0) {
        state.newTreeDetail.forEach(list => {
          const hasitem = _.filter(state.oldTags, res => {
            res.closable = false;
            return res.deviceNumber === list.deviceNumber;
          });
          if (hasitem.length === 1) {
            list.checked = true;
            list.disabled = true;
          } else {
            list.checked = false;
            state.tags = [];
          }
        });
      }
    };
    // searchItem
    const searchItem = value => {
      if (value) {
        state.newTreeDetail = [];
        state.newTreeDetail = state.newTreeDetail.concat(
          state.treeDetail.filter(item => {
            return item.name.indexOf(value) !== -1 || item.deviceNumber.indexOf(value) !== -1;
          })
        );
      } else {
        state.newTreeDetail = state.treeDetail;
      }
    };

    const filterClear = () => {
      state.newTreeDetail = state.treeDetail;
    };

    // 全选
    const selectAll = arr => {
      if (arr && arr.length > 0) {
        state.tags = [];
        arr.forEach(item => {
          const hasitem = _.filter(state.oldTags, res => {
            res.closable = false;
            return res.deviceNumber === item.deviceNumber;
          });
          if (hasitem.length === 1) {
            item.checked = true;
            item.disabled = true;
          } else {
            item.checked = true;
            state.tags.push(item);
            // filterItem(item)
          }
        });
      }
    };
    // 反选
    const selectNone = arr => {
      if (arr && arr.length > 0) {
        arr.forEach(item => {
          if (!item.disabled) {
            if (item.checked) {
              item.checked = false;
              state.tags = state.tags.filter(val => {
                return val.deviceNumber !== item.deviceNumber;
              });
            } else {
              item.checked = true;
              state.tags.push(item);
              // filterItem(item)
            }
          }
        });
      }
    };

    const getEquipmentList = id => {
      // 获取检测项目list
      state.loading = true;
      const params = {
        deviceCategoryId: id === '-1' ? '' : id,
        param: state.param,
        page: '1',
        limit: '-1'
      };
      getList(params).then(res => {
        state.loading = false;
        if (res) {
          const data = res.data.data.list;
          state.treeDetail = data;
          if (state.filterText && state.treeDetail.length > 0) {
            state.newTreeDetail = state.treeDetail.filter(item => {
              return JSON.stringify(item).indexOf(state.filterText) !== -1;
            });
          } else {
            state.newTreeDetail = state.treeDetail;
          }
          state.newTreeDetail.forEach(item => {
            const hasitem = _.filter(state.oldTags, res => {
              res.closable = false;
              return res.deviceNumber === item.deviceNumber;
            });
            // 判断是否已经勾选过但未曾确认选择
            const hasitem2 = _.filter(state.tags, res => {
              res.closable = false;
              return res.deviceNumber === item.deviceNumber;
            });
            if (hasitem.length === 1) {
              item.checked = true;
              item.disabled = true;
            } else {
              if (hasitem2.length === 1) {
                item.checked = true;
              } else {
                item.checked = false;
              }
            }
          });
        }
      });
    };

    return {
      ...toRefs(state),
      getEquipmentList,
      selectAll,
      selectNone,
      emptyImg,
      clear,
      searchItem,
      dialogSuccess,
      close,
      filterNode,
      clickNode,
      treeRef,
      closeTag,
      changeCheckBox,
      filterClear
    };
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  .tree-container {
    .tree-content {
      height: calc(100vh - 520px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 480px);
    overflow-y: auto;
    .el-radio-group {
      width: 100%;
    }
    .item-content {
      padding-bottom: 0 !important;
      .left {
        height: 32px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .main {
        width: 100%;
        .title {
          overflow: hidden; /* 确保超出容器的内容被裁剪 */
          white-space: nowrap; /* 确保文本在一行内显示 */
          text-overflow: ellipsis; /* 超出部分显示省略号 */
        }
        .item-list {
          width: 100%;
          border-left: none;
          .item-box {
            height: 16px;
            margin-right: 20px !important;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
  .topSelect {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
