<template>
  <div
    v-if="pageType === '1'"
    id="issuId1"
    class="listContent"
    @mouseenter="monseenter"
    @mouseleave="mouseleave"
    @mouseenter.prevent
  >
    <el-row v-for="(item, index) in list" :key="item.id" class="listLi">
      <el-col :span="2">{{ index + 1 }}</el-col>
      <el-col :span="7">{{ item.secSampleNum || '--' }}</el-col>
      <el-col :span="5">{{ item.applicantName || '--' }}</el-col>
      <el-col :span="10">{{ item.type === 1 ? item.supplierName || '--' : item.customerName || '--' }}</el-col>
    </el-row>
  </div>
  <div
    v-if="pageType === '2'"
    id="issuId2"
    class="listContent"
    @mouseenter="monseenter"
    @mouseleave="mouseleave"
    @mouseenter.prevent
  >
    <el-row v-for="(item, index) in list" :key="item.id" class="listLi">
      <el-col :span="2">{{ index + 1 }}</el-col>
      <el-col :span="6">{{ item.secSampleNum || '--' }}</el-col>
      <el-col :span="4">{{ getNameByid(item.ownerId) || '--' }}</el-col>
      <el-col :span="6">
        <span v-for="(val, i) in item.ownerIds.split(',')" :key="i">
          {{ getNameByid(val) || val || '--' }}
        </span>
        &nbsp;
      </el-col>
      <el-col :span="6" class="textLeft"
        >{{ formatDate(item.finishedDate) || '--' }}
        <span v-if="exceedTime(item.finishedDate)" class="exceedTime">+{{ exceedTime(item.finishedDate) }}</span>
      </el-col>
    </el-row>
  </div>
  <div
    v-if="pageType === '3'"
    id="issuId3"
    class="listContent"
    @mouseenter="monseenter"
    @mouseleave="mouseleave"
    @mouseenter.prevent
  >
    <el-row v-for="(item, index) in list" :key="item.id" class="listLi">
      <el-col :span="2">{{ index + 1 }}</el-col>
      <el-col :span="10">{{ item.secSampleNum || '--' }}</el-col>
      <el-col :span="12">
        <span v-for="(val, i) in item.reviewerId.split(',')" :key="i">
          {{ getNameByid(val) || '--' }}
        </span>
      </el-col>
    </el-row>
  </div>
  <div
    v-if="pageType === '4'"
    id="issuId4"
    class="listContent"
    @mouseenter="monseenter"
    @mouseleave="mouseleave"
    @mouseenter.prevent
  >
    <el-row v-for="(item, index) in list" :key="item.id" class="listLi">
      <el-col :span="2">{{ index + 1 }}</el-col>
      <el-col :span="10">{{ item.secSampleNum || '--' }}</el-col>
      <el-col :span="12">{{ getNameByid(item.ownerId) || '--' }}</el-col>
    </el-row>
  </div>
</template>
<script>
import { nextTick, onBeforeUnmount, reactive, toRefs, watch } from 'vue';
import { samplesList } from '@/api/order';
import { getsampleList } from '@/api/execution';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { getUnReportList } from '@/api/dataBoard';

export default {
  name: 'ScrollList',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  emits: ['pageTotal'],
  setup(props, context) {
    const state = reactive({
      list: [],
      total: 0,
      type: '',
      pageType: '',
      queryPage: {
        limit: '30',
        page: '1'
      },
      timer1: null
    });
    watch(
      props,
      newValue => {
        state.pageType = props.type;
        nextTick(() => {
          getList(true);
          // setTimer1()
        });
      },
      { immediate: true }
    );
    // 请求接口
    const getList = isFirst => {
      if (isFirst) {
        // 第一次加载
        if (state.pageType === '1') {
          samplesList({ status: '0', ...state.queryPage }).then(res => {
            transferTotal(res.data.data);
          });
        }
        if (state.pageType === '2') {
          getsampleList({ status: '2', ...state.queryPage }).then(res => {
            transferTotal(res.data.data);
          });
        }
        if (state.pageType === '3') {
          getsampleList({ status: '3', ...state.queryPage }).then(res => {
            transferTotal(res.data.data);
          });
        }
        if (state.pageType === '4') {
          getUnReportList(state.queryPage).then(res => {
            transferTotal(res.data.data);
          });
        }
      } else if (state.total > 0 && state.list.length <= state.total) {
        // 不是第一次加载，已经加载过了并且有数据
        if (state.pageType === '1') {
          samplesList({ status: '0', ...{ page: '1', limit: state.total.toString() } }).then(res => {
            const data = res.data.data;
            state.list = data.list;
            nextTick(() => {
              setTimer1();
            });
          });
        }
        if (state.pageType === '2') {
          getsampleList({ status: '2', ...{ page: '1', limit: state.total.toString() } }).then(res => {
            const data = res.data.data;
            state.list = data.list;
            nextTick(() => {
              setTimer1();
            });
          });
        }
        if (state.pageType === '3') {
          getsampleList({ status: '3', ...{ page: '1', limit: state.total.toString() } }).then(res => {
            const data = res.data.data;
            state.list = data.list;
            nextTick(() => {
              setTimer1();
            });
          });
        }
        if (state.pageType === '4') {
          getUnReportList({ page: '1', limit: state.total.toString() }).then(res => {
            const data = res.data.data;
            state.list = data.list;
            nextTick(() => {
              setTimer1();
            });
          });
        }
      }
    };
    const setTimer1 = () => {
      if (state.timer1 === null) {
        state.timer1 = setInterval(() => {
          if (document.getElementById('issuId' + state.pageType)) {
            // 可滚动内容的高度
            const scrollHeight = document.getElementById('issuId' + state.pageType).scrollHeight;
            // 可视区域的高度（内容实际高度+上下内边距）
            const clientHeight = document.getElementById('issuId' + state.pageType).clientHeight;
            const heightDifference = scrollHeight - clientHeight;
            // scroll height：readable and writable
            if (document.getElementById('issuId' + state.pageType).scrollTop >= heightDifference) {
              removeTimer1();
              // make it go back to original location after one second
              setTimeout(() => {
                // document.getElementById(id).scrollTop = 0
                getList(true);
              }, 1000);
            } else {
              document.getElementById('issuId' + state.pageType).scrollTop++;
            }
          }
        }, 44);
      }
    };
    const transferTotal = data => {
      state.total = data.totalCount;
      nextTick(() => {
        document.getElementById('issuId' + state.pageType).scrollTop = 0;
      });
      context.emit('pageTotal', { total: state.total, type: 'total' + state.pageType });
      if (state.total > 0) {
        if (state.list.length <= 5 && state.list.length > 0) {
          setTimeout(() => {
            getList(false);
          }, 10000);
        } else {
          getList(false);
        }
      } else {
        setTimeout(() => {
          getList(true);
        }, 10000);
      }
    };
    const removeTimer1 = () => {
      if (state.timer1) {
        clearInterval(state.timer1);
        state.timer1 = null;
      }
    };
    const monseenter = () => {
      removeTimer1();
    };
    const mouseleave = () => {
      setTimer1();
    };
    onBeforeUnmount(() => {
      removeTimer1();
    });
    // 计算超期多少天
    const exceedTime = time => {
      const nowDays = new Date().getTime();
      const endTime = new Date(time).getTime();
      var residueDays;
      if (nowDays > endTime) {
        residueDays = Math.floor((nowDays - endTime) / (24 * 3600 * 1000));
      }
      return residueDays;
    };
    return {
      ...toRefs(state),
      removeTimer1,
      transferTotal,
      exceedTime,
      formatDate,
      getNameByid,
      monseenter,
      mouseleave,
      setTimer1,
      getList
    };
  }
};
</script>

<style lang="scss" scoped>
@import '../../data-board.scss';
</style>
