<template>
  <el-dialog
    :model-value="show"
    custom-class="custom-dialog"
    title="判定标准"
    width="60%"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterText"
          class="search"
          size="small"
          placeholder="请输入产品名称"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="7">
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="treeRef"
                :data="tree"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="17">
          <div class="list-container">
            <el-table
              ref="tableRef"
              :key="tableKey"
              v-loading="loading"
              :data="newTreeDetail"
              fit
              border
              size="medium"
              height="auto"
              highlight-current-row
              class="dark-table allocation-table base-table format-height-table"
              @select="selectChange"
              @selection-change="handleSelectionChange"
              @current-change="changeRadio"
            >
              <!-- <el-table-column type="selection" width="55" /> -->
              <el-table-column type="index" label="选择" width="70" align="center">
                <template #default="{ row }">
                  <el-radio v-model="row.radio" :label="row.id" @change="changeRadio(row)">{{ '' }}</el-radio>
                </template>
              </el-table-column>
              <el-table-column label="产品名称" prop="name" show-overflow-tooltip>
                <template #default="{ row }">
                  <span style="white-space: pre">{{ row.productName || '--' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="更新时间" prop="lastUpdateDateTime" :width="130" sortable>
                <template #default="{ row }">
                  <span>{{ formatDate(row.lastUpdateDateTime) || '--' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="更新人" prop="user" :width="130">
                <template #default="{ row }">
                  <UserTag :name="getNameByid(row.lastUpdateByuserId) || '--'" />
                </template>
              </el-table-column>
              <el-table-column label="版本" prop="version" :width="100" style="text-align: left">
                <template #default="{ row }">
                  <span>{{ row.version ? 'V' + row.version : row.version }}</span>
                </template>
              </el-table-column>
              <el-table-column label="状态" prop="status" :width="100" style="text-align: left">
                <template #default="{ row }">
                  <el-tag size="small" :type="statusDicClass[row.status]"> {{ statusDic[row.status] }}</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
      <div v-if="matchList.length" class="matchContent">
        <div class="matchTitle">快速选择：</div>
        <div class="matchList">
          <el-tag v-for="item in matchList" :key="item.id" class="blue matchItem" @click="quickSelection(item)">{{
            item.productName
          }}</el-tag>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button v-if="isShowBtn" @click="dialogSuccess">仅选择标准</el-button>
        <el-button type="primary" @click="addStandardItem">选择检测项目</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, inject, nextTick } from 'vue';
import { getProductList } from '@/api/testBase';
import _ from 'lodash';
import { formatDate } from '@/utils/formatTime';
import { getNameByid } from '@/utils/common';
import UserTag from '@/components/UserTag';
import { ElMessage } from 'element-plus';
import { standardproductMatch } from '@/api/allocation';

export default {
  name: 'JudgementStandard',
  components: { UserTag },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    isShowBtn: {
      type: Boolean,
      default: true
    },
    tree: {
      type: Object,
      default: function () {
        return {};
      }
    },
    standardModel: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'selectData', 'showStandardItem'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const lodash = inject('_');
    const tableRef = ref(null);
    const datas = reactive({
      showDialog: props.show,
      inputRef: ref(),
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'code'
      },
      tags: [],
      oldTags: [],
      standardModelInfo: {},
      matchList: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      tableKey: 0,
      currentTreeNode: {},
      statusDicClass: {
        0: 'info',
        1: 'success',
        2: 'wait'
      },
      statusDic: {
        2: '停用',
        1: '生效',
        0: '草稿'
      },
      selectedRow: {}
    });

    watch(
      () => props.show,
      newValue => {
        datas.showDialog = newValue;
        datas.tags = [];
        datas.standardModelInfo = props.standardModel;
        if (newValue && props.tree && props.tree.length > 0) {
          datas.currentTreeNode = props.tree[0];
          if (datas.standardModelInfo.standardModel) {
            getMatchingInfo({
              materialCategoryCode: datas.currentTreeNode.materialCategoryCode,
              standardCategoryId: datas.currentTreeNode.id
            });
          }
          proxy.getCapabilityList();
          nextTick(() => {
            datas.inputRef.focus();
          });
        }
      },
      { deep: true }
    );
    const getMatchingInfo = treeNode => {
      const params = {
        params: datas.filterText,
        isReleased: true,
        sampleModelDetail: datas.standardModelInfo.sampleModelDetail,
        materialCategoryCode: treeNode.materialCategoryCode,
        standardCategoryId: treeNode.id
      };
      standardproductMatch(params).then(res => {
        if (res) {
          datas.matchList = res.data.data;
        }
      });
    };
    // 过滤树节点
    const treeRef = ref(null);

    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      datas.currentTreeNode = data;
      getMatchingInfo(datas.currentTreeNode);
      proxy.getCapabilityList();
    };

    // 确定选择
    const dialogSuccess = () => {
      if (checkSelectedRow()) {
        context.emit('selectData', datas.selectedRow);
        datas.showDialog = false;
        context.emit('close', false);
        datas.selectedRow = {};
      }
    };

    // 添加标准项目
    const addStandardItem = () => {
      if (checkSelectedRow()) {
        context.emit('showStandardItem', datas.selectedRow);
      }
    };

    function checkSelectedRow() {
      if (!datas.selectedRow.id) {
        ElMessage.warning('请先选择一条数据!');
        return false;
      }

      return true;
    }

    // 取消选择
    const close = () => {
      datas.showDialog = false;
      context.emit('close', false);
      datas.selectedRow = {};
    };
    // 关闭tags
    const closeTag = tag => {
      // console.log(tag)
      const hasitem = _.filter(datas.newTreeDetail, res => {
        if (res.id === tag.id) {
          res.checked = false;
        }
        return res.id === tag.id;
      });
      if (hasitem.length > 0) {
        tableRef.value.toggleRowSelection(hasitem[0]);
      }
      datas.tags.splice(datas.tags.indexOf(tag), 1);
    };
    // 清空
    const clear = () => {
      datas.tags = [];
      if (datas.newTreeDetail && datas.newTreeDetail.length > 0) {
        tableRef.value.clearSelection();
      }
    };
    // searchItem 查询
    const searchItem = value => {
      if (value) {
        proxy.getCapabilityList();
      } else {
        datas.filterText = '';
        proxy.getCapabilityList();
      }
    };
    // table 选择事件
    const handleSelectionChange = val => {
      if (val.length > 0 && datas.newTreeDetail.length > 0 && val.length === datas.newTreeDetail.length) {
        datas.newTreeDetail.forEach(tree => {
          tree.checked = true;
        });
      } else if (val.length === 0) {
        datas.newTreeDetail.forEach(tree => {
          tree.checked = false;
        });
        _.pullAll(datas.tags, datas.newTreeDetail);
      }
      datas.tags = datas.tags.concat(val);
      datas.tags = lodash.uniqBy(datas.tags, 'id');
    };
    const selectChange = (val, row) => {
      row.checked = !row.checked;
      if (row.checked === false) {
        datas.tags.splice(datas.tags.indexOf(row), 1);
      }
    };

    const changeRadio = row => {
      datas.selectedRow = row;
      row.radio = row.id;
      datas.newTreeDetail.forEach(item => {
        if (item.id !== row.id) {
          item.radio = false;
        }
      });
    };
    const quickSelection = item => {
      const selectIndex = datas.newTreeDetail.findIndex(val => {
        return item.id === val.id;
      });
      datas.newTreeDetail[selectIndex].radio = item.id;
      changeRadio(item);
    };

    return {
      ...toRefs(datas),
      searchItem,
      quickSelection,
      dialogSuccess,
      close,
      filterNode,
      clickNode,
      treeRef,
      closeTag,
      clear,
      handleSelectionChange,
      selectChange,
      formatDate,
      getNameByid,
      tableRef,
      changeRadio,
      addStandardItem
    };
  },
  methods: {
    getCapabilityList() {
      // 获取检测项目list
      const _this = this;
      const params = {
        param: _this.filterText,
        standardCategoryId: _this.currentTreeNode.id,
        isReleased: true,
        materialCategoryCode: _this.currentTreeNode.materialCategoryCode,
        page: '1',
        limit: '-1'
      };
      _this.loading = true;
      getProductList(params).then(response => {
        // console.log(response.data.data)
        // console.log(_this.oldTags)
        if (response !== false) {
          const { data } = response.data;
          const filterList = [];
          // 判定标准只能选择启用的项目
          if (data.list && data.list.length > 0) {
            data.list.forEach(fl => {
              if (fl.status === 1) {
                filterList.push(fl);
              }
            });
          }
          _this.treeDetail = filterList;
          _this.newTreeDetail = filterList;
          if (_this.newTreeDetail && _this.newTreeDetail.length > 0) {
            _this.$nextTick(() => {
              // console.log(_this.$refs.tableRef)
              _this.newTreeDetail.forEach(item => {
                item.checked = false;
                item.radio = false;
                const hasitem = _.filter(_this.tags, res => {
                  return res.id === item.id;
                });
                // console.log(hasitem)
                if (hasitem.length >= 1) {
                  item.checked = true;
                  _this.$refs.tableRef.toggleRowSelection(item);
                }
              });
            });
          }
          setTimeout(() => {
            _this.loading = false;
          }, 1.5 * 500);
        } else {
          setTimeout(() => {
            _this.loading = false;
          }, 1.5 * 500);
        }
      });
    }
  }
};
</script>

<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.matchItem {
  display: inline-block;
  margin: 0 10px 10px 0;
}
.matchList {
  max-height: 110px;
  overflow-y: auto;
}
.matchContent {
  background-color: #fff;
  padding: 10px 10px 0 10px;
  margin-bottom: 10px;
}
.matchTitle {
  line-height: 20px;
}
.dialog-content {
  margin-bottom: 0;
}
.tree-container {
  margin-bottom: 20px;
  .tree-content {
    height: calc(100vh - 500px);
    overflow-y: auto;
  }
}
.list-container {
  padding-bottom: 0;
  margin-bottom: 20px;
  height: calc(100vh - 460px);
  overflow: hidden;
  .dark-table {
    :deep(.el-table__body-wrapper) {
      max-height: calc(100vh - 545px);
      overflow-y: auto;
      overflow-x: hidden !important;
    }
  }
}
</style>
