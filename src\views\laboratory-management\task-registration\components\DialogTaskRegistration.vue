<template>
  <el-dialog
    v-model="showDialog"
    custom-class="double-dialog tiny-dialog info-add"
    :title="title"
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div v-loading="loading" class="dialog-main">
      <el-form
        ref="inspectionInfoRef"
        class="formDataInfo"
        :model="formInline"
        :rules="inspectionInfoRule"
        label-width="110px"
        label-position="top"
      >
        <el-row :gutter="60">
          <el-col :span="24">
            <el-form-item label="" prop="entrustType">
              <el-radio-group
                v-model="formInline.entrustType"
                size="large"
                class="radio-groups"
                :disabled="isEdit"
                @change="changeType"
              >
                <el-radio-button v-for="item in typeOptions" :key="item.id" :label="item.id">
                  <img :src="item.icon" alt="icon" />
                  {{ item.name }}
                  <span class="corner" />
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-space direction="vertical">
          <el-row :gutter="60">
            <el-col :span="12">
              <el-form-item label="委托单编号：" prop="entrustNo">
                <el-input v-model="formInline.entrustNo" type="text" maxlength="50" placeholder="请输入委托单编号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="登记人：" prop="regUserId">
                <el-select
                  v-model="formInline.regUserId"
                  class="owner-select"
                  placeholder="请选择登记人"
                  clearable
                  filterable
                  :filter-method="filterUserList"
                  @focus="filterUserList(null)"
                  @change="changeUser"
                >
                  <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="物资分类：" prop="materialCode">
                <el-select
                  v-model="formInline.materialCode"
                  class="owner-select"
                  placeholder="请选择物资分类"
                  clearable
                  filterable
                  :filter-method="filterMaterialList"
                  @focus="filterMaterialList(null)"
                  @change="changeMaterialType"
                >
                  <el-option v-for="item in materialOptions" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="登记日期：" prop="regDate">
                <el-date-picker
                  v-model="formInline.regDate"
                  type="date"
                  placeholder="请选择登记日期"
                  class="register-date"
                  @change="changeRegisterTime"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="截止日期：" prop="endDate">
                <el-date-picker
                  v-model="formInline.endDate"
                  type="date"
                  placeholder="请选择登记日期"
                  class="register-date"
                  @change="changeEndDate"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务类型：" prop="serviceType">
                <el-radio-group v-model="formInline.serviceType">
                  <el-radio :label="0" size="large">标准服务</el-radio>
                  <el-radio :label="1" size="large">加急服务</el-radio>
                  <el-radio :label="2" size="large">特急服务</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="样品回收：" prop="sampleRecycle">
                <el-radio-group id="sample-recycle" v-model="formInline.sampleRecycle">
                  <el-radio :label="0" size="large">否</el-radio>
                  <el-radio :label="1" size="large">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="盖章范围：">
                <el-checkbox v-model="formInline.isCnas">CNAS</el-checkbox>
                <el-checkbox v-model="formInline.isCma">CMA</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="领取方式：" prop="receiveMethod">
                <el-radio-group v-model="formInline.receiveMethod">
                  <el-radio :label="0" size="large">自取</el-radio>
                  <el-radio :label="1" size="large">到付</el-radio>
                  <el-radio :label="2" size="large">顺丰（注：加收费用20元）</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="样品验收：" prop="sampleAccept">
                <el-select v-model="formInline.sampleAccept" clearable placeholder="请选择样品验收">
                  <el-option :value="0" label="符合" />
                  <el-option :value="1" label="不符合" />
                  <el-option :value="2" label="转实验室验收" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报告语言：">
                <el-row>
                  <el-col :span="12">
                    <el-checkbox v-model="formInline.reportCn" @change="changeReport(0)">中文报告 </el-checkbox></el-col
                  >
                  <el-col :span="12">
                    <span v-show="formInline.reportCn"
                      ><el-input-number
                        v-show="formInline.reportCn"
                        v-model="formInline.reportCnNum"
                        size="small"
                        :min="0"
                        :max="999"
                        controls-position="right"
                      />份</span
                    >
                  </el-col>
                  <el-col :span="6" />
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-checkbox v-model="formInline.reportEn" @change="changeReport(1)">英文报告 </el-checkbox></el-col
                  >
                  <el-col :span="12">
                    <span v-show="formInline.reportEn"
                      ><el-input-number
                        v-model="formInline.reportEnNum"
                        size="small"
                        :min="0"
                        :max="999"
                        controls-position="right"
                      />份</span
                    >
                  </el-col>
                  <el-col :span="6" />
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-checkbox v-model="formInline.reportBilingual" @change="changeReport(2)"
                      >中英文报告
                    </el-checkbox></el-col
                  >
                  <el-col :span="12">
                    <span v-show="formInline.reportBilingual"
                      ><el-input-number
                        v-model="formInline.reportBilingualNum"
                        size="small"
                        :min="0"
                        :max="999"
                        controls-position="right"
                      />份</span
                    >
                  </el-col>
                  <el-col :span="6" />
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-checkbox
                      v-model="formInline.reportBilingualNone"
                      style="display: inline-block; width: 30%"
                      @change="changeReport(3)"
                      >只需结果，不需报告
                    </el-checkbox></el-col
                  >
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="报告编制：">
                <el-row>
                  <el-col :span="12">
                    <el-radio-group v-model="formInline.reportPreparation">
                      <el-radio :label="0" size="large">一份委托单出一份报告</el-radio>
                    </el-radio-group>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-radio-group v-model="formInline.reportPreparation">
                      <el-radio :label="1" size="large">相同项目出一份报告</el-radio>
                    </el-radio-group>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-radio-group v-model="formInline.reportPreparation">
                      <el-radio :label="2" size="large">一个样品出一份报告</el-radio>
                    </el-radio-group>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-radio-group v-model="formInline.reportPreparation">
                      <el-radio :label="3" size="large">不出具报告</el-radio>
                    </el-radio-group>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="8">
                    <el-radio-group v-model="formInline.reportPreparation">
                      <el-radio :label="4" size="large">其他要求</el-radio>
                    </el-radio-group>
                  </el-col>
                  <el-col :span="16">
                    <el-input
                      v-model="formInline.reportPreparationContent"
                      maxlength="30"
                      placeholder="请输入其他要求内容"
                    />
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注：" prop="remark">
                <el-input
                  v-model="formInline.remark"
                  :rows="2"
                  type="textarea"
                  maxlength="300"
                  placeholder="输入备注内容"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-space>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="loading" @click="handleClose()">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="dialogSuccess" @keyup.prevent @keydown.enter.prevent
          >保 存</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref } from 'vue';
import _ from 'lodash';
import { ElMessage } from 'element-plus';
import router from '@/router/index.js';
// import { useRoute } from 'vue-router'
import { saveTaskRegistrationInfo } from '@/api/task-registration';
import { useStore } from 'vuex';
import { getLoginInfo } from '@/utils/auth';
import { getProcessListNew } from '@/api/mas';
import { formatDate } from '@/utils/formatTime';
import { taskType } from '@/data/industryTerm';
import { getNameByid } from '@/utils/common';
import { isZw } from '@/utils/validate';

export default {
  name: 'DialogTaskRegistration',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '新增委托'
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    // console.log(proxy)
    // const lodash = inject('_')
    const store = useStore().state;
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      materialOptions: store.user.materialList,
      copyMaterialOptions: store.user.materialList,
      showDialog: false,
      formInline: {
        endDate: '',
        entrustCost: '',
        entrustNo: '',
        entrustType: 0,
        id: '',
        isCma: false,
        isCnas: false,
        justResult: true,
        regDate: formatDate(new Date()),
        regUserId: getLoginInfo().accountId,
        regUserName: getNameByid(getLoginInfo().accountId),
        remark: '',
        reportBilingualNone: false,
        reportBilingual: false,
        reportBilingualNum: 0,
        reportCn: true,
        reportCnNum: 1,
        reportEn: false,
        reportEnNum: 0,
        sampleRecycle: 0,
        serviceType: 0,
        status: 0,
        materialCode: store.user.materialList[0]?.code || '',
        materialName: store.user.materialList[0]?.name || '',
        receiveMethod: 2,
        reportPreparation: 2,
        sampleAccept: 2
      },
      inspectionInfo: {
        list: []
      },
      inspectionInfoRef: ref(),
      inspectionInfoRule: {
        materialCode: [{ required: true, message: '请选择物资分类' }],
        regUserId: [{ required: true, message: '请选择登记人' }],
        serviceType: [{ required: true, message: '请选择服务类型' }],
        sampleRecycle: [{ required: true, message: '请选择样品回收' }],
        regDate: [{ required: true, message: '请选择登记日期' }],
        type: [{ required: true, message: '请输入检验类型' }],
        entrustNo: [{ validator: isZw, required: false, tigger: 'blur' }]
      },
      typeOptions: taskType,
      loading: false,
      processList: [],
      copyProcessList: [],
      showEdit: false
    });

    watch(
      () => props.show,
      newValue => {
        if (newValue) {
          datas.showDialog = newValue;
          datas.showEdit = props.isEdit;
          if (props.isEdit) {
            datas.formInline = JSON.parse(JSON.stringify(props.info));
            datas.inspectionInfoRule.entrustNo[0].required = true;
            if (!datas.formInline.reportCn && !datas.formInline.reportEn && !datas.formInline.reportBilingual) {
              datas.formInline.reportBilingualNone = true;
            }
          }
        }
      },
      { deep: true }
    );

    // 确定选择
    const dialogSuccess = () => {
      datas.inspectionInfoRef.validate(async valid => {
        if (valid) {
          datas.loading = true;
          const response = await saveTaskRegistrationInfo(datas.formInline).finally((datas.loading = false));
          if (response) {
            datas.showDialog = false;
            ElMessage.success(datas.showEdit ? '更新成功' : '新增成功');
            context.emit('close', true);
            if (!datas.showEdit) {
              router.push({
                name: 'TaskRegistrationDetail',
                query: { id: response.data.data, flag: 2 }
              });
            }
          }
        }
      });
    };
    // 取消选择
    const handleClose = () => {
      datas.showDialog = false;
      context.emit('close', false);
    };
    // 过滤物资分类
    const filterMaterialList = val => {
      if (val) {
        const list = [];
        datas.copyMaterialOptions.forEach(material => {
          const item = _.filter(material.name, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (material.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(material);
          }
        });
        datas.materialOptions = list;
      } else {
        datas.materialOptions = datas.copyMaterialOptions;
      }
    };
    // 过滤登记人
    const filterUserList = val => {
      if (val) {
        const list = [];
        datas.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        datas.userOptions = list;
      } else {
        datas.userOptions = datas.copyUserOptions;
      }
    };
    // 登记人-change
    const changeUser = id => {
      datas.formInline.regUserId = id;
      datas.formInline.regUserName = getNameByid(id);
    };

    // 登记人-change
    const changeMaterialType = materialCode => {
      const materialIndex = datas.copyMaterialOptions.findIndex(item => item.code === materialCode);
      datas.formInline.materialName = materialIndex === -1 ? '' : datas.copyMaterialOptions[materialIndex];
      datas.formInline.materialCode = materialCode;
    };
    // 选择登记日期
    const changeRegisterTime = time => {
      datas.formInline.regDate = formatDate(time);
    };

    const changeEndDate = time => {
      datas.formInline.endDate = formatDate(time);
    };

    // 检验类型-change
    const changeType = type => {
      datas.formInline.entrustType = type;
    };
    // 生产工序-change
    const changeProductionProcedure = no => {
      // console.log(no)
      datas.processList.forEach(item => {
        if (item.no === no) {
          datas.formInline.productionProcedure = item.name;
          datas.formInline.productionProcedureNo = item.no;
        }
      });
    };
    // 选择入库日期
    const changeInputWarehouseDate = time => {
      // console.log(time)
    };

    function changeReport(value) {
      if (value === 0) {
        datas.formInline.reportCnNum = datas.formInline.reportCn ? 1 : 0;
        datas.formInline.reportBilingualNone = 0;
      } else if (value === 1) {
        datas.formInline.reportEnNum = datas.formInline.reportEn ? 1 : 0;
        datas.formInline.reportBilingualNone = 0;
      } else if (value === 2) {
        datas.formInline.reportBilingualNum = datas.formInline.reportBilingual ? 1 : 0;
        datas.formInline.reportBilingualNone = 0;
      } else {
        if (datas.formInline.reportBilingualNone) {
          datas.formInline.reportCn = false;
          datas.formInline.reportCnNum = 0;
          datas.formInline.reportEn = false;
          datas.formInline.reportEnNum = 0;
          datas.formInline.reportBilingual = false;
          datas.formInline.reportBilingualNum = 0;
        } else {
          datas.formInline.reportCn = true;
          datas.formInline.reportCnNum = 1;
        }
      }
    }

    return {
      ...toRefs(datas),
      dialogSuccess,
      handleClose,
      filterUserList,
      filterMaterialList,
      changeUser,
      changeMaterialType,
      changeRegisterTime,
      changeEndDate,
      changeType,
      changeReport,
      changeProductionProcedure,
      changeInputWarehouseDate
    };
  },
  created() {
    this.getProcessLists();
  },
  methods: {
    // 获取生产工序列表接口
    getProcessLists() {
      var that = this;
      var param = {
        limit: '-1',
        page: '1',
        content: ''
      };
      getProcessListNew(param).then(res => {
        if (res !== false) {
          that.processList = res.data.data.list;
          that.copyProcessList = res.data.data.list;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
#sample-recycle {
  :deep(.el-radio__label) {
    margin-right: 10px;
  }
}

.formDataInfo {
  :deep(.el-input--medium .el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
}
.scdd {
  position: absolute;
  left: 70px;
  top: -34px;
}
.info-add {
  .dialog-main {
    :deep(.el-space) {
      width: 100%;
      .el-space__item {
        margin: 16px 0;
        width: 100%;
      }
    }

    .radio-groups {
      display: flex;
      justify-content: space-around;
      align-items: center;
      .el-radio-button {
        border: 2px solid transparent;
      }
      .el-radio-button.is-active {
        border-color: $tes-primary3;
      }
      .el-radio-button__inner {
        img {
          width: 54px;
          height: 64px;
          margin-right: 20px;
        }
      }
      :deep(.el-radio-button__inner) {
        border-color: transparent;
        // border-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
        padding: 16px 40px;
        box-shadow: none;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        line-height: 28px;
      }
      .el-radio-button {
        box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
        // border-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
      }
      .el-radio-button__original-radio:checked + .el-radio-button__inner {
        border: 2px solid $tes-primary3 !important;
      }

      .corner {
        opacity: 0;
        position: absolute;
        top: -20px;
        right: -20px;
        width: 0;
        height: 0;
        border: 20px solid $tes-primary;
        border-bottom-color: transparent;
        border-top-color: transparent;
        border-left-color: transparent;
        transform: rotateZ(135deg);
      }
      .el-radio-button.is-active .corner {
        opacity: 1;
      }
    }
    .el-input--medium .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
    .el-divider--horizontal {
      margin: 15px 0px;
    }
    .el-form-item {
      text-align: left;
      // width: 46%;
      margin-bottom: 10px;
      :deep(.el-form-item__label) {
        font-weight: normal;
        font-size: 14px;
        color: #606266;
        padding: 0px;
      }
      :deep(.el-form-item__content) {
        .el-select {
          width: 100%;
        }
        .el-date-editor.el-input {
          width: 100%;
        }
        .el-radio-group {
          .el-radio {
            margin: 0px;
          }
          .el-radio:nth-child(2) {
            margin: 0px 5px;
          }
        }
      }
    }
  }
}
</style>
