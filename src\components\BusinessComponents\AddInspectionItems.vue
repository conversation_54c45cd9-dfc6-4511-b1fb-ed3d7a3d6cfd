<template>
  <!-- 检测项目弹出框，多选 -->
  <el-dialog
    v-model="showDialog"
    custom-class="custom-dialog"
    title="新增项目"
    width="60%"
    top="5vh"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <div class="dialog-header">
      <div class="header-left">
        <el-input
          ref="inputRef"
          v-model="filterText"
          v-trim
          class="search"
          size="small"
          placeholder="请输入项目名称"
          prefix-icon="el-icon-search"
          maxlength="100"
          clearable
          @clear="filterClear()"
          @keyup.enter="searchItem(filterText)"
        />
        <el-button type="primary" size="small" @click="searchItem(filterText)">查询</el-button>
      </div>
      <div class="header-right">
        <el-button size="small" @click="selectNone(newTreeDetail)" @keyup.prevent @keydown.enter.prevent
          >反选</el-button
        >
        <el-button size="small" @click="selectAll(treeDetail)" @keyup.prevent @keydown.enter.prevent>全选</el-button>
      </div>
    </div>
    <div class="dialog-content">
      <el-row>
        <el-col :span="6">
          <el-select
            v-model="materialCode"
            filterable
            size="small"
            :disabled="!isSelect"
            class="topSelect"
            placeholder="请选择物资分类"
            @change="changeMaterialCode"
          >
            <el-option v-for="val in materialList" :key="val.value" :label="val.name" :value="val.code" />
          </el-select>
          <div class="tree-container">
            <div class="tree-content">
              <el-tree
                ref="leftTreeRef"
                :data="treeData"
                node-key="id"
                :props="defaultProps"
                default-expand-all
                :expand-on-click-node="false"
                :highlight-current="true"
                draggable
                class="leftTree"
                @node-click="clickNode"
              >
                <template #default="{ node }">
                  <span>{{ node.label }}</span>
                </template>
              </el-tree>
            </div>
          </div>
        </el-col>
        <el-col :span="18">
          <div v-loading="loading" class="list-container">
            <el-row
              v-for="(item, index) in newTreeDetail"
              :key="index"
              class="item-content"
              style="width: 100%"
              @click="changeCheckBox(item)"
            >
              <el-col :span="1">
                <div class="left">
                  <el-checkbox v-model="item.checked" :disabled="item.disabled" @change="changeCheckBox(item, 1)" />
                </div>
              </el-col>
              <el-col :span="22">
                <div class="main">
                  <div class="title" :class="{ 'title-checked': item.checked }">{{ item.name }}</div>
                  <div class="item-list">
                    <el-tag
                      v-for="(list, index1) in item.capabilityparaVoList"
                      :key="index1"
                      :type="item.checked ? 'primary' : 'info'"
                      >{{ list.name }}</el-tag
                    >
                  </div>
                </div>
              </el-col>
              <el-col :span="1">
                <div style="float: right">
                  <i
                    v-if="item.templateVersion"
                    style="color: green"
                    class="el-icon--right el-icon-document-checked"
                  /><i v-else style="color: red" class="el-icon--right el-icon-document-delete" />
                </div>
              </el-col>
            </el-row>
            <el-empty v-if="newTreeDetail.length === 0" :image="emptyImg" description="暂无数据" />
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-other">
      <div class="title">
        <label>已选项目</label>
        <el-button v-if="tags.length > 0" size="small" icon="el-icon-delete" @click="clear">清空</el-button>
      </div>
      <div v-if="oldTags.length > 0 || tags.length > 0" class="select-items">
        <el-tag
          v-for="tag in oldTags"
          :key="tag.name || tag.sourceName"
          :closable="tag.closable"
          size="small"
          @close="closeTag(tag)"
        >
          {{ tag.name || tag.sourceName }}
        </el-tag>
        <el-tag v-for="tag in tags" :key="tag.name || tag.sourceName" closable size="small" @close="closeTag(tag)">
          {{ tag.name || tag.sourceName }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="dialogSuccess">确定选择</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, watch, ref, getCurrentInstance, inject, nextTick } from 'vue';
import { getCapabilityUplist, getCapabilityTree } from '@/api/user';
import { formatTree } from '@/utils/formatJson';
import { useStore } from 'vuex';
import _ from 'lodash';
import emptyImg from '@/assets/img/empty-data.png';
// import { ElLoading } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'AddInspectionItems',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    canSelectMaterial: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    },
    materialSelectCode: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const lodash = inject('_');
    const store = useStore();
    const datas = reactive({
      showDialog: props.show,
      materialList: [],
      isSelect: false,
      materialCode: '',
      leftTreeRef: ref(),
      treeData: [],
      inputRef: ref(),
      filterText: '',
      pageType: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      tags: [],
      oldTags: [],
      treeDetail: [],
      newTreeDetail: [],
      loading: false,
      isRefresh: true
    });

    watch(props, newValue => {
      datas.showDialog = newValue.show;
      if (datas.showDialog) {
        datas.filterText = '';
        datas.pageType = newValue.type;
        datas.isSelect = newValue.canSelectMaterial;
        datas.showDialog = newValue.show;
        datas.materialList = store.state.user.materialList;
        if (store.state.user.materialList.length > 0) {
          datas.materialCode = newValue.materialSelectCode || store.state.user.materialList[0].code;
        }
        if (datas.materialList.length > 0) {
          getLeftTree();
        }

        nextTick(() => {
          datas.inputRef.focus();
        });
      }
      datas.tags = [];
      datas.oldTags = JSON.parse(JSON.stringify(newValue.data));
    });

    // 过滤树节点
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    // 点击树节点
    const clickNode = (data, node) => {
      proxy.getCapabilityList(data.id, data.materialCategoryCode);
    };

    // 确定选择
    const dialogSuccess = () => {
      context.emit('selectData', datas.tags);
      datas.showDialog = false;
      datas.isRefresh = true;
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      datas.isRefresh = true;
      context.emit('close', false);
    };
    // 全选
    const selectAll = arr => {
      if (arr && arr.length > 0) {
        datas.tags = [];
        arr.forEach(item => {
          const hasitem = _.filter(datas.oldTags, res => {
            res.closable = false;
            return res.id === item.id || item.id === res.sourceid || res.capabilityId === item.id;
          });
          if (hasitem.length === 1) {
            item.checked = true;
            item.disabled = true;
          } else {
            item.checked = true;
            filterItem(item);
          }
          // datas.tags.push(item)
        });
      }
    };
    // 反选
    const selectNone = arr => {
      if (arr && arr.length > 0) {
        arr.forEach(item => {
          if (!item.disabled) {
            if (item.checked) {
              item.checked = false;
              datas.tags = datas.tags.filter(val => {
                return val.name !== item.name;
              });
            } else {
              item.checked = true;
              filterItem(item);
            }
          }
        });
      }
    };
    // changeCheckBox
    const changeCheckBox = (item, flag) => {
      if (item.disabled) {
        return false;
      }
      item.checked = !item.checked;
      if (item.checked) {
        filterItem(item, 1);
      } else {
        lodash.remove(datas.tags, n => {
          return item.id === n.id || item.id === n.capabilityId;
        });
      }
    };
    // 数据过滤
    const filterItem = (item, number) => {
      const newItem = {
        name: item.name,
        number: item.number,
        capabilityId: item.id,
        categoryName: item.categoryName,
        capabilityparaVoList: item.capabilityparaVoList
      };
      datas.tags.push(newItem);
    };
    // 关闭tags
    const closeTag = tag => {
      datas.tags.splice(datas.tags.indexOf(tag), 1);
      datas.newTreeDetail.forEach(item => {
        const hasitem = _.filter(datas.oldTags, res => {
          res.closable = false;
          return res.id === item.id || item.id === res.sourceid || res.capabilityId === item.id;
        });
        // 判断是否已经勾选过但未曾确认选择
        const hasitem2 = _.filter(datas.tags, res => {
          res.closable = false;
          return res.id === item.id || item.id === res.sourceid || res.capabilityId === item.id;
        });
        if (hasitem.length === 1) {
          item.checked = true;
          item.disabled = true;
        } else {
          if (hasitem2.length === 1) {
            item.checked = true;
          } else {
            item.checked = false;
          }
        }
      });
    };
    // 清空
    const clear = () => {
      datas.tags = [];
      if (datas.newTreeDetail && datas.newTreeDetail.length > 0) {
        datas.newTreeDetail.forEach(list => {
          const hasitem = _.filter(datas.oldTags, res => {
            res.closable = false;
            return res.id === list.id || list.id === res.sourceid || res.capabilityId === list.id;
          });
          if (hasitem.length === 1) {
            list.checked = true;
            list.disabled = true;
          } else {
            list.checked = false;
            datas.tags = [];
          }
        });
      }
    };
    // searchItem
    const searchItem = value => {
      if (value) {
        datas.newTreeDetail = [];
        datas.newTreeDetail = datas.newTreeDetail.concat(
          datas.treeDetail.filter(item => {
            return JSON.stringify(item).indexOf(value) !== -1;
          })
        );
      } else {
        datas.newTreeDetail = datas.treeDetail;
      }
    };

    const filterClear = () => {
      datas.newTreeDetail = datas.treeDetail;
    };
    // 初始化树节点
    const getLeftTree = () => {
      getCapabilityTree(datas.materialCode).then(res => {
        const treeData = formatTree(res.data.data);
        if (treeData.length > 0) {
          datas.treeData = JSON.parse(JSON.stringify(treeData));
          const all = { id: '-1', name: '全部', materialCategoryCode: datas.materialCode };
          datas.treeData.unshift(all);
          nextTick(() => {
            datas.leftTreeRef.setCurrentKey('-1', true);
          });
          clickNode(datas.treeData[0]);
        } else {
          datas.treeData = [];
          datas.newTreeDetail = [];
        }
      });
    };
    const changeMaterialCode = val => {
      getLeftTree();
    };
    return {
      ...toRefs(datas),
      getLeftTree,
      changeMaterialCode,
      emptyImg,
      searchItem,
      dialogSuccess,
      close,
      filterNode,
      selectAll,
      selectNone,
      clickNode,
      closeTag,
      clear,
      changeCheckBox,
      filterItem,
      filterClear
    };
  },
  methods: {
    getCapabilityList(id, materialCategoryCode) {
      // 获取检测项目list
      const vm = this;
      vm.loading = true;
      getCapabilityUplist(id, materialCategoryCode).then(response => {
        vm.loading = false;
        if (response) {
          const { data } = response.data;
          vm.treeDetail = [];
          if (vm.pageType === 'strategyList') {
            data.forEach(item => {
              const newChild = [];
              item.capabilityparaVoList.forEach(val => {
                if (val.applylabel.includes('2')) {
                  newChild.push(val);
                }
              });
              vm.treeDetail.push({ ...item, capabilityparaVoList: newChild });
            });
          } else {
            vm.treeDetail = data;
          }
          if (vm.filterText && vm.treeDetail.length > 0) {
            vm.newTreeDetail = vm.treeDetail.filter(item => {
              return JSON.stringify(item).indexOf(vm.filterText) !== -1;
            });
          } else {
            vm.newTreeDetail = vm.treeDetail;
          }
          vm.newTreeDetail.forEach(item => {
            const hasitem = _.filter(vm.oldTags, res => {
              res.closable = false;
              return res.id === item.id || item.id === res.sourceId || res.capabilityId === item.id;
            });
            // 判断是否已经勾选过但未曾确认选择
            const hasitem2 = _.filter(vm.tags, res => {
              res.closable = false;
              return res.id === item.id || item.id === res.sourceId || res.capabilityId === item.id;
            });
            if (hasitem.length === 1) {
              item.checked = true;
              item.disabled = true;
            } else {
              if (hasitem2.length === 1) {
                item.checked = true;
              } else {
                item.checked = false;
              }
            }
          });
        }
      });
    }
  }
};
</script>
<style lang="scss">
@import '@/styles/dialog.scss';
</style>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.dialog-content {
  .tree-container {
    .tree-content {
      height: calc(100vh - 500px);
      overflow-y: auto;
      padding-left: 0;
    }
  }
  .list-container {
    height: calc(100vh - 418px);
    overflow-y: auto;
  }
  .topSelect {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style>
