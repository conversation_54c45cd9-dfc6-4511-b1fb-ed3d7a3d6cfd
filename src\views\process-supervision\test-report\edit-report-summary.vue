<template>
  <el-container v-loading.fullscreen.lock="loadingStep2" class="edit-report-summary">
    <el-empty v-if="tableData.length === 0" :image="emptyImg" description="暂无数据" />
    <div v-else style="display: flex; width: 100%">
      <el-aside v-show="showAside" :style="{ width: asideWidth + 'px' }">
        <p class="title">检测项目</p>
        <div :key="asideKey" class="left-main">
          <div
            v-for="(item, index) in tableData"
            :key="item.id"
            class="tree-list nowrap"
            :title="item.name"
            @click="scrollJump(index)"
          >
            <span
              class="line"
              :class="{
                'is-current': currentIndex === index,
                'red-border': item.expResult === results.find(item => item.code === '1')?.name,
                'green-border': item.expResult === results.find(item => item.code === '0')?.name,
                'yellow-border': item.expResult === ''
              }"
            />
            <span class="sortingIcon tes-move iconfont" style="font-size: 10px; cursor: move; margin-right: 5px" />
            <span v-if="item.retestSourceId" class="custom-icon text-copy">复</span>
            <span v-if="item.isRetest === 1" class="custom-icon text-origin">源</span>
            <span v-if="item.isEntrust === 1" class="custom-icon text-green">外</span>
            <span class="text">{{ item.name }}</span>
          </div>
        </div>
      </el-aside>
      <el-tooltip effect="dark" :content="collapseTip" :hide-after="0" placement="right">
        <drag-handle
          :class="{ 'close-handle': !showAside }"
          @widthChange="widthChange"
          @mouseup="collapseLeft"
          @mousedown="setOldAsideWidth"
        />
      </el-tooltip>
      <el-main style="height: calc(100vh - 24rem); overflow-y: auto; padding: 0px">
        <div v-for="(item, index) in tableData" :key="index" class="main-content">
          <div class="content-header expanded">
            <el-checkbox
              v-model="item.selectFlag"
              class="checkbox-cell"
              true-label="1"
              false-label="0"
              @change="changeSelectFlag(item)"
            />
            <div class="title">{{ item.name || '--' }}</div>
            <el-button
              v-if="getPermissionBtn('TestReportTemplateButton')"
              size="mini"
              type="primary"
              style="position: absolute; right: 0px"
              @click="handleCheckTemplate(item)"
              >原始记录</el-button
            >
            <el-tooltip content="检测项目完成情况" placement="top" effect="light">
              <el-tag size="small" :type="item.status == 5 ? 'success' : 'info'">
                {{ filterStatus(item.status) }}</el-tag
              >
            </el-tooltip>
          </div>
          <div class="content-header flex-between">
            <div>
              <span class="label">检测时间：</span>
              <span>{{ formatDate(item.completeDateTime) || '--' }}</span>
            </div>
            <div>
              <span class="label">试验员：</span>
              <span>{{ getNamesByIds(item.ownerId) || '--' }}</span>
            </div>
            <div>
              <span class="label">判定标准：</span>
              <el-select
                ref="selectRefs"
                v-model="item.standardProductId"
                filterable
                size="small"
                placeholder="请选择"
                class="header-select"
                @change="changeMethod(item.standardProductId, item)"
              >
                <el-option
                  v-for="itemChild in item.methods"
                  :key="itemChild.standardProductId"
                  :label="itemChild.standardProductName"
                  :value="itemChild.standardProductId"
                />
              </el-select>
            </div>
            <div>
              <span class="label">结论：</span>
              <el-select
                v-model="item.expResult"
                filterable
                size="small"
                clearable
                placeholder="请选择结论"
                class="header-select"
                @change="val => changeItemResult(val, item)"
              >
                <el-option
                  v-for="itemResult in results"
                  :key="itemResult.id"
                  :label="itemResult.name"
                  :value="itemResult.name"
                />
              </el-select>
            </div>
          </div>
          <el-table
            :data="item.childList"
            fit
            border
            highlight-current-row
            class="dark-table base-table expand-table"
            :row-class-name="rowClassName"
            @header-dragend="drageHeader"
          >
            <el-table-column
              prop="selectFlag"
              :label="Number(item.selectFlag) ? '启用' : ''"
              :width="Number(item.selectFlag) ? colWidth.checkbox : 10"
            >
              <template #default="scope">
                <el-checkbox
                  v-if="Number(item.selectFlag)"
                  v-model="scope.row.selectFlag"
                  class="checkbox-cell"
                  true-label="1"
                  false-label="0"
                  @change="changeParamSelectFlag(scope.row, item)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="name" label="关键参数" :width="180" show-overflow-tooltip>
              <template #default="scope"> {{ scope.row.name }} </template>
            </el-table-column>
            <el-table-column prop="expRequirement" label="技术要求" :width="180" show-overflow-tooltip>
              <template #default="scope">
                <el-input
                  v-model="scope.row.expRequirement"
                  placeholder="输入技术要求"
                  size="small"
                  :title="scope.row.expRequirement"
                  @change="changeExp(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column prop="expValueUnit" label="单位" :width="120">
              <template #default="scope">
                <el-select
                  v-model="scope.row.expValueUnit"
                  placeholder="选择"
                  clearable
                  filterable
                  size="small"
                  @change="changeUnit(scope.row)"
                >
                  <el-option
                    v-for="itemUnit in units"
                    :key="itemUnit.id"
                    :label="itemUnit.name"
                    :value="itemUnit.name"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="检测结果" :min-width="180">
              <template #default="{ row, $index }">
                <el-row class="item-input" :gutter="20">
                  <el-col
                    v-for="(v, indexValue) in row.value"
                    :key="indexValue"
                    :span="row.value.length >= 3 ? 8 : 24"
                    class="input-group"
                    :class="{ group: v.colourValue, marginBottom: row.value.length === 2 || row.value.length > 3 }"
                  >
                    <!-- <div v-if="v.colourValue.length>0" :style="{width: v.colourValue?v.colourValue.length*20+'px':''}">{{ v.colourValue }}</div> -->
                    <div v-if="v.isZs">
                      <el-input
                        v-model="v.baseNumber"
                        placeholder="请输入"
                        size="small"
                        :disabled="!getPermissionBtn('EditResult')"
                        :title="v.paraValue"
                        style="width: 50%"
                        @change="
                          val => {
                            return changeZs(val, v.exponentNumber, row, index, indexValue);
                          }
                        "
                        @blur="handleBlurDs(v.baseNumber, v.paraValue, indexValue, index, $index)"
                      />
                      <div class="zsNumber">
                        x10
                        <el-input
                          v-model.number="v.exponentNumber"
                          :disabled="!getPermissionBtn('EditResult')"
                          class="exponent"
                          placeholder=""
                          size="small"
                          :title="v.paraValue"
                          @change="
                            val => {
                              return changeZs(v.baseNumber, val, row, index, indexValue);
                            }
                          "
                        />
                      </div>
                    </div>
                    <el-input
                      v-else
                      v-model="v.paraValue"
                      :disabled="!getPermissionBtn('EditResult')"
                      :placeholder="getPlaceHolder(row)"
                      size="small"
                      :title="v.paraValue"
                      @change="changeTestResult(row, index)"
                    >
                      <template v-if="v.colourValue" #prepend>
                        <el-tooltip :content="v.colourValue" placement="top" effect="light">
                          <span>{{ v.colourValue }}</span>
                        </el-tooltip>
                      </template>
                    </el-input>
                  </el-col>
                  <el-col v-if="row.expValueSize > 1" :span="24">
                    <el-input
                      v-model="row.value.result_1_2"
                      :disabled="!getPermissionBtn('EditResult')"
                      placeholder="请输入"
                      size="small"
                    />
                  </el-col>
                  <el-col v-if="row.expValueSize > 2" :span="24">
                    <el-input
                      v-model="row.value.result_1_3"
                      :disabled="!getPermissionBtn('EditResult')"
                      placeholder="请输入"
                      size="small"
                    />
                  </el-col>
                </el-row>
              </template>
            </el-table-column>
            <el-table-column prop="expResult" label="结论" :width="120">
              <template #default="scope">
                <el-select
                  v-model="scope.row.expResult"
                  placeholder="选择结论"
                  clearable
                  filterable
                  size="small"
                  @change="changeResult(scope.row, index)"
                >
                  <el-option
                    v-for="itemResult in results"
                    :key="itemResult.id"
                    :label="itemResult.name"
                    :value="itemResult.name"
                  />
                </el-select>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-main>
    </div>
  </el-container>
</template>

<script>
import { reactive, ref, toRefs, onMounted, nextTick, watch, onBeforeUnmount } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getFloatByNum, filterUnit, getReservedSign } from '@/utils/formatJson';
import { getNamesByIds, getPermissionBtn } from '@/utils/common';
import { useStore } from 'vuex';
import { getSelectChanged, getReportExpInfo, resetReportCapability } from '@/api/testReport';
import Sortable from 'sortablejs';
import { drageHeader } from '@/utils/formatTable';
import { getCurrentReportInfo } from '@/utils/auth';
// import { UserTag } from '@/components/UserTag'
// import { ElMessage } from 'element-plus'
import _ from 'lodash';
import { getDictionary } from '@/api/user';
// import $ from 'jquery'
import DragHandle from '@/components/DragHandle/handle.vue';
import { colWidth } from '@/data/tableStyle';
import { publicIsNumber } from '@/utils/validate';
import { ElMessageBox } from 'element-plus';
import router from '@/router/index.js';
import emptyImg from '@/assets/img/empty-table.png';
// import ReportTemplate from './components/ReportTemplate.vue'
// import baseExcel from '@/views/excelComponents/baseExcel.vue'
// import {
//   experiment,
//   TemplateIdByexperimentId
// } from '@/api/execution'

export default {
  name: 'EditReportSummary',
  components: { DragHandle },
  props: {
    table: {
      type: Array,
      default: function () {
        return [];
      }
    },
    select: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['setInfo'],
  setup(props, context) {
    const stores = useStore().state.user;
    const currentInfo = getCurrentReportInfo();
    const tableRef = ref(null);
    const state = reactive({
      tableData: props.table,
      templateJsonData: {
        isStandardCustom: 0
      }, // 模板信息
      detailData: props.data,
      tableKey: 'tableKey1',
      units: stores.unit,
      results: [
        { id: '1', name: '合格' },
        { id: '2', name: '不合格' },
        { id: '3', name: '不判定' }
      ],
      scrollInfo: {
        scrollTop: 0,
        dom: ''
      },
      noWatch: false,
      currentIndex: 0,
      summarySelectJSON: {},
      summarySelectArray: [],
      loadingStep2: false,
      selectRefs: ref(),
      allValues: [],
      showAside: true,
      asideWidth: 280,
      asideMaxWidth: 600,
      asideMinWidth: 200,
      oldAsideWidth: 300,
      collapseTip: '点击折叠左面板',
      asideKey: 0
    });

    // 过滤分配状态
    const filterStatus = status => {
      var name = '';
      switch (status) {
        case -2:
          name = '作废';
          break;
        case 1:
          name = '新增-待分配';
          break;
        case '2':
        case 2:
          name = '已分配-待提交';
          break;
        case 3:
          name = '待审核';
          break;
        case 4:
          name = '退回拒绝';
          break;
        case 5:
          name = '已通过';
          break;
        case -5:
          name = '删除';
          break;
      }
      return name;
    };

    // 选择checkbox
    const handleSelectionChange = (val, row) => {
      if (val.length > 0) {
        state.tableData.forEach(list => {
          const hasitem = _.filter(val, res => {
            return res.id === list.id;
          });
          if (hasitem.length === 1) {
            list.selectFlag = '1';
          } else {
            list.selectFlag = '0';
          }
          // 复测，源只能选择一个
          const hasitem1 = _.filter(val, res => {
            return res.sourceId === list.sourceId;
          });
          if (hasitem1.length > 1) {
            list.selectFlag = '0';
            // tableRef.value.toggleRowSelection(list, false)
            if (list.id === row.id) {
              hasitem1.forEach(hi => {
                if (hi.id === row.id) {
                  list.selectFlag = '1';
                  // tableRef.value.toggleRowSelection(hi, true)
                }
              });
            }
          }
        });
      }
      context.emit('setInfo', state.tableData);
    };
    // 判定标准-change
    const changeMethod = (value, row) => {
      row.standardProduct = row.methods[value].standardProductName;
      row.standardProductVersion = row.methods[value].standardProductVersion;
      getSelectsChanged(row);
      context.emit('setInfo', state.tableData);
    };
    // 项目结论-change
    const changeItemResult = (val, row) => {
      context.emit('setInfo', state.tableData);
    };
    // 结论-change
    const changeResult = (item, index) => {
      judgmentItemResult(state.tableData[index].childList).then(res => {
        state.tableData[index].expResult = res;
      });
      context.emit('setInfo', state.tableData);
    };
    // 关键参数-检测结果-change
    const changeTestResult = (row, index) => {
      if (
        row.resultType &&
        row.resultType.toString() === '1' &&
        publicIsNumber(row.smallnumber) &&
        row.resultOptionType === 0
      ) {
        row.value?.forEach(item => {
          item.paraValue = publicIsNumber(item.paraValue)
            ? Number(item.paraValue).toFixed(Number(row.smallnumber))
            : '';
        });
      } else if (
        row.resultType &&
        row.resultType.toString() === '1' &&
        publicIsNumber(row.smallnumber) &&
        row.resultOptionType === 1
      ) {
        row.value?.forEach(item => {
          // Number(numStr).toExponential(number - 1)
          item.paraValue = publicIsNumber(item.paraValue) ? getReservedSign(item.paraValue, row.smallnumber) : '';
        });
      }
      context.emit('setInfo', state.tableData);
    };

    const getPlaceHolder = row => {
      let placeholder = '请输入';
      if (
        row.resultType &&
        row.resultType.toString() === '1' &&
        publicIsNumber(row.smallnumber) &&
        row.resultOptionType === 0
      ) {
        placeholder = `请输入数字, 保留${row.smallnumber}位小数, 输入后将自动修正`;
      } else if (
        row.resultType &&
        row.resultType.toString() === '1' &&
        publicIsNumber(row.smallnumber) &&
        row.resultOptionType === 1
      ) {
        placeholder = `请输入数字, 保留${row.smallnumber}位有效数字, 输入后将自动修正`;
      }
      return placeholder;
    };

    // 技术要求-change
    const changeExp = row => {
      context.emit('setInfo', state.tableData);
    };
    // 单位-change
    const changeUnit = row => {
      context.emit('setInfo', state.tableData);
    };
    // 向下滑动
    const smoothDown = (distance, step, total) => {
      if (distance < total) {
        distance += step;
        state.scrollInfo.dom.scrollTop = distance;
        setTimeout(function () {
          smoothDown(distance, step, total);
        }, 10);
      } else {
        state.scrollInfo.dom.scrollTop = total;
      }
    };
    // 向上滑动
    const smoothUp = (distance, step, total) => {
      if (distance > total) {
        distance -= step;
        state.scrollInfo.dom.scrollTop = distance;
        setTimeout(function () {
          smoothUp(distance, step, total);
        }, 10);
      } else {
        state.scrollInfo.dom.scrollTop = total;
      }
    };
    // 用 class 添加锚点
    const scrollJump = index => {
      state.currentIndex = index;
      const jump = document.querySelectorAll('.main-content');
      const expand = document.querySelectorAll('.checkbox-cell');
      var total = 0;
      for (var i = 0; i < index; i++) {
        total = total + jump[i].offsetHeight + expand[i].offsetHeight;
      }
      const distance = state.scrollInfo.scrollTop;
      // 平滑滚动，时长500ms，每10ms一跳，共50跳
      var step = total / 50;
      if (total > distance) {
        smoothDown(distance, step, total);
      } else {
        const newTotal = distance - total;
        step = newTotal / 50;
        smoothUp(distance, step, total);
      }
    };
    // 获取滚动高度
    const onScroll = e => {
      state.scrollInfo.scrollTop = e.target.scrollTop;
    };

    // 判定数值型是否合格
    const filterResult = (expValue, maxNum, minNum, minselected, maxseleced) => {
      var flag = true;
      var hasItemValue = _.filter(expValue, v => {
        return v.paraValue;
      });
      if (hasItemValue.length !== expValue.length) {
        return '';
      }
      if (expValue && expValue.length > 0 && maxNum && minNum) {
        expValue.forEach(ev => {
          if (
            (parseFloat(ev.paraValue) < parseFloat(minNum) || parseFloat(ev.paraValue) > parseFloat(maxNum)) &&
            ev.paraValue !== '/'
          ) {
            flag = false;
          }
          if (
            (minselected !== true && minselected !== 1 && parseFloat(ev.paraValue) === parseFloat(minNum)) ||
            (maxseleced !== true && maxseleced !== 1 && parseFloat(ev.paraValue) === parseFloat(maxNum))
          ) {
            flag = false;
          }
        });
      } else if (expValue && expValue.length > 0 && maxNum) {
        expValue.forEach(ev => {
          if (parseFloat(ev.paraValue) > parseFloat(maxNum) && ev.paraValue !== '/') {
            flag = false;
          }
          if (maxseleced !== true && maxseleced !== 1 && parseFloat(ev.paraValue) === parseFloat(maxNum)) {
            flag = false;
          }
        });
      } else if (expValue && expValue.length > 0 && minNum) {
        expValue.forEach(ev => {
          if (parseFloat(ev.paraValue) < parseFloat(minNum) && ev.paraValue !== '/') {
            flag = false;
          }
          if (minselected !== true && minselected !== 1 && parseFloat(ev.paraValue) === parseFloat(minNum)) {
            flag = false;
          }
        });
      } else {
        return '';
      }
      if (flag) {
        return state.results.find(item => item.code === '0')?.name;
      } else {
        return state.results.find(item => item.code === '1')?.name;
      }
    };
    // 判定枚举型、自定义枚举型是否合格
    const filterMJResult = (dList, qualifiedOption, noQualifiedOption, values) => {
      var trueFlag = 0;
      var falseFlag = 0;
      if (dList && dList.length > 0 && values.length > 0) {
        values.forEach(v => {
          var qItems = _.filter(dList, dl => {
            return qualifiedOption.indexOf(dl.code) !== -1 && v.paraValue === dl.name;
          });
          if (qItems.length > 0) {
            trueFlag += 1;
          }
          var noqItems = _.filter(dList, dl => {
            return noQualifiedOption.indexOf(dl.code) !== -1 && v.paraValue === dl.name;
          });
          if (noqItems.length > 0) {
            falseFlag += 1;
          }
        });
        if (trueFlag === values.length) {
          return state.results.find(item => item.code === '0')?.name;
        } else if (falseFlag > 0 && falseFlag + trueFlag === values.length) {
          return state.results.find(item => item.code === '1')?.name;
        } else {
          return '';
        }
        // dList.forEach(d => {
        //   if (qualifiedOption.indexOf(d.code) !== -1) {
        //     // isTrue = '不合格'
        //     if (values.length > 0) {
        //       values.forEach(v => {
        //         if (v.paraValue.indexOf(d.name) !== -1) {
        //           isTrue = '合格'
        //         }
        //       })
        //     }
        //   } else if (noQualifiedOption.indexOf(d.code) !== -1) {
        //     values.forEach(v => {
        //       if (v.paraValue.indexOf(d.name) !== -1) {
        //         isTrue = '不合格'
        //       }
        //     })
        //   }
        // })
      }
      // return isTrue
    };
    // 格式化自定义枚举选项
    const filterOption = array => {
      array.forEach(item => {
        item.name = item.label;
        item.code = item.value;
      });
      return array;
    };
    // 非标准数值型判断是否合格
    const filterStanderMathResult = (values, capabilityJson, javascriptMath) => {
      var paramObj = {
        expResult: '',
        expRequirement: ''
      };
      var valueNumList = [];
      if (capabilityJson.length > 0 && state.allValues.length > 0) {
        var hasParam = true;
        capabilityJson.forEach(json => {
          const flag = _.filter(state.allValues, v => {
            return v.paraKey.indexOf(json.templatekey) !== -1;
          });
          if (flag.length > 0) {
            const num = flag[0].paraValue ? parseFloat(flag[0].paraValue) : 0;
            valueNumList.push(num);
          } else {
            hasParam = false;
          }
        });
        // 如果参数值key匹配不到,说明没有该参数，无法进行解析
        if (hasParam) {
          // 没有线芯的情况
          if (values.length === 1) {
            const num1 = values[0].paraValue ? parseFloat(values[0].paraValue) : 0;
            const obj = analysisJS(javascriptMath, capabilityJson, valueNumList, num1);
            paramObj.expResult = obj.expResult;
            paramObj.expRequirement = '基准值为:' + obj.standardValue;
          } else if (values.length > 1) {
            // 多线芯情况
            var isTrue = true;
            values.forEach(item => {
              const num1 = item.paraValue ? parseFloat(item.paraValue) : 0;
              const obj = analysisJS(javascriptMath, capabilityJson, valueNumList, num1);
              paramObj.expRequirement = '基准值为:' + obj.standardValue;
              if (obj.expResult === state.results.find(item => item.code === '1')?.name) {
                isTrue = false;
              }
            });
            if (isTrue) {
              paramObj.expResult = state.results.find(item => item.code === '0')?.name;
            } else {
              paramObj.expResult = state.results.find(item => item.code === '1')?.name;
            }
          }
        } else {
          paramObj.expRequirement = '缺少参数值，无法解析';
        }
      }
      return paramObj;
    };

    const filterSummaryList = (asideDropReset = true) => {
      if (state.tableData.length > 0) {
        state.tableData.forEach((val, index) => {
          // 过滤判定标准
          val.methods = val.select ? (val.select === undefined ? {} : val.select) : {};
          // 过滤判定结论
          if (val.childList === null) {
            val.childList = [];
          } else if (val.childList.length > 0) {
            // var resultFlag = true
            // var resultNum = 0
            val.childList.forEach(async cml => {
              cml.selectFlag = val.selectFlag;
              if (cml.expValueUnit) {
                cml.expValueUnit = filterUnit(cml.expValueUnit);
              }
              console.log(cml, cml.value === undefined || cml.value.length === 0);
              if (cml.value === undefined || cml.value.length === 0) {
                cml.value = [{ colourValue: '', paraKey: '', paraValue: '' }];
              } else {
                cml.value.forEach(item => {
                  if (/^[+-]?[\d]+([\.][\d]+)?([Ee][+-]?[\d]+)?$/.test(item.paraValue)) {
                    const array = item.paraValue.toString().split('e');
                    if (array.length > 1) {
                      item.isZs = true;
                      item.exponentNumber = Number(array[1]);
                      item.baseNumber = Number(array[0]);
                    }
                  }
                });
                if (cml.expResult === '' || cml.expResult === null || cml.expResult === undefined) {
                  if (cml.isDetermine === true || cml.isDetermine === 'true') {
                    // 数值型判定
                    if (cml.resultType?.toString() === '1') {
                      // 数值型分为标准型和非标准型 standardmathtype:非标准为'0'，标准为'1'
                      if (cml.standardmathtype?.toString() === '1') {
                        cml.expResult = filterResult(
                          cml.value,
                          cml.maxNum,
                          cml.minNum,
                          cml.minSelected,
                          cml.maxSeleced
                        );
                      } else {
                        const capabilityJson = cml.capabilityjson ? JSON.parse(cml.capabilityjson) : [];
                        const javascriptMath = cml.javascriptmath ? cml.javascriptmath : '';
                        if (javascriptMath && capabilityJson.length > 0) {
                          const resultObj = filterStanderMathResult(cml.value, capabilityJson, javascriptMath);
                          cml.expResult = resultObj.expResult;
                          cml.expRequirement = resultObj.expRequirement;
                        } else {
                          cml.expResult = '';
                          cml.expRequirement = '条件不足，无法解析';
                        }
                      }
                    } else if (cml.resultType === '2' || cml.resultType === '5') {
                      if (cml?.custlabel) {
                        // 枚举型、自定义枚举判定
                        const dList =
                          cml.resultType === '2'
                            ? await getResultDictionary(cml.custlabel)
                            : filterOption(JSON.parse(cml.custlabel));
                        cml.expResult = filterMJResult(dList, cml.qualifiedOption, cml.noQualifiedOption, cml.value);
                      }
                    } else if (cml.resultType === '3') {
                      cml.expResult = cml.expResult || '合格';
                    }
                  } else if (cml.isDetermine === false || cml.isDetermine === 'false') {
                    cml.expResult = state.results.find(item => item.code === '2')?.name;
                  } else {
                    cml.expResult = '';
                  }
                }
              }
              // 判定检测项目是否合格
              if (cml.expResult === state.results.find(item => item.code === '1')?.name) {
                // resultFlag = false
              } else if (cml.expResult === state.results.find(item => item.code === '0')?.name) {
                // resultNum += 1
              }
            });
            if (!val.localList.length) {
              // 如果第一次进来，则根据关键参数判断一下检测项目的结论，否则直接取原先的值
              judgmentItemResult(val.childList).then(res => {
                val.expResult = res;
              });
            }
            // if (resultFlag && resultNum === val.childList.length) {
            //   val.expResult = state.results[0].name
            // } else if (resultFlag && resultNum !== val.childList.length) {
            //   val.expResult = ''
            // } else {
            //   val.expResult = state.results[1].name
            // }
          }
          // 如果reportStandardResponseList接口有返回，就用此判定标准
          if (state.summarySelectArray.length > 0) {
            val.methods = state.summarySelectJSON;
            if (val.standardProductId === '' && Object.values(val.methods).length > 0) {
              val.standardProductId = Object.values(val.methods)[0].standardProductId;
              val.standardProduct = Object.values(val.methods)[0].standardProductName;
              val.standardProductVersion = Object.values(val.methods)[0].standardProductVersion;
            }
          }
        });
        context.emit('setInfo', state.tableData);
        nextTick(() => {
          state.tableData.forEach((val, index) => {
            const hasitem = _.filter(state.tableData, res => {
              return res.sourceId === val.sourceId;
            });
            if (hasitem.length > 1) {
              var num = 0;
              const newHasItem = [];
              hasitem.forEach(item => {
                if (item.selectFlag === '1') {
                  num += 1;
                  newHasItem.push(item);
                }
              });
              if (num > 1) {
                newHasItem.forEach((nhi, index) => {
                  if (index === 0) {
                    nhi.selectFlag = '1';
                  } else {
                    nhi.selectFlag = '0';
                  }
                });
              }
            }
          });
          if (asideDropReset) {
            rowDrop();
          }
          state.scrollInfo.dom = document.getElementsByClassName('el-main')[0];
          state.scrollInfo.dom.addEventListener('scroll', onScroll);
          // 监听鼠标滚动事件
          window.addEventListener('mousewheel', handleScroll, false) ||
            window.addEventListener('DOMMouseScroll', handleScroll, false);
        });
      }
    };
    const rowClassName = ({ row }) => {
      if (row.applyLabel) {
        if (row.applyLabel.indexOf('2') === -1) {
          return 'hidden-row';
        }
        if (row.expResult === '2' || row.expResult === '不合格') {
          return 'highlight-row';
        } else {
          return '';
        }
      } else {
        if (row.expResult === '2' || row.expResult === '不合格') {
          return 'highlight-row';
        } else {
          return '';
        }
      }
    };
    // 判断项目结论
    const judgmentItemResult = childArray => {
      return new Promise(resolve => {
        var itemResult = '';
        const childResult = Array.from(
          new Set(
            childArray.map(item => {
              return item.expResult;
            })
          )
        );
        if (childResult.length === 1) {
          itemResult = childResult[0];
        } else if (childResult.length === 2) {
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            // 含有不合格的，直接为不合格
            itemResult = '不合格';
          } else if (
            childResult.some(item => {
              return item === '不符合要求';
            })
          ) {
            // 含有不符合要求的，直接为不符合要求
            itemResult = '不符合要求';
          } else if (
            childResult.some(item => {
              return item === '合格';
            })
          ) {
            // 含有合格的，其他为不判定和空，直接为合格
            itemResult = '合格';
          } else if (
            childResult.some(item => {
              return item === '符合要求';
            })
          ) {
            // 含有符合要求的，其他为不判定和空，直接为符合要求
            itemResult = '符合要求';
          } else {
            // 只有不判定、空两种，检测项目结论为空
            itemResult = '';
          }
        } else if (childResult.length === 3) {
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            // 含有不合格的，直接为不合格
            itemResult = '不合格';
          } else if (
            childResult.some(item => {
              return item === '不符合要求';
            })
          ) {
            // 含有不符合要求的，直接为不符合要求
            itemResult = '不符合要求';
          } else if (
            childResult.some(item => {
              return item === '合格';
            })
          ) {
            // 只有不判定、空和合格三种，检测项目结论为合格
            itemResult = '合格';
          } else {
            // 只有不判定、空和符合要求三种，检测项目结论为符合要求
            itemResult = '符合要求';
          }
        } else {
          // 四种值都有，结论为不合格
          if (
            childResult.some(item => {
              return item === '不合格';
            })
          ) {
            itemResult = '不合格';
          } else {
            // 四种值都有，结论为不符合要求
            itemResult = '不符合要求';
          }
        }
        resolve(itemResult);
      });
    };
    // 鼠标滚动时候，让判定标准下拉框隐藏
    const handleScroll = () => {
      if (state.tableData.length > 0) {
        state.tableData.forEach((td, index) => {
          if (state.selectRefs[2 * index]) {
            state.selectRefs[2 * index].blur();
          }
        });
      }
    };

    const getItemResultList = () => {
      getDictionary('JCXMSHJLB').then(res => {
        state.results = res.data.data?.dictionaryoption;
      });
    };

    onMounted(() => {
      getItemResultList();
      nextTick(async () => {
        state.tableData = await getReportSummaryInfo(currentInfo);
        state.tableData.forEach(item => {
          if (!state.summarySelectJSON[item.standardProductId] && state.summarySelectArray[0]) {
            item.standardProductId = state.summarySelectArray[0].standardProductId;
            item.standardProduct = state.summarySelectArray[0].standardProductName;
            item.standardProductVersion = state.summarySelectArray[0].standardProductVersion;
            getSelectsChanged(item);
          }
        });
        filterSummaryList();
      });
    });

    onBeforeUnmount(() => {
      window.removeEventListener('mousewheel', handleScroll, false) ||
        window.removeEventListener('DOMMouseScroll', handleScroll, false);
    });

    // 获取枚举型字典
    const getResultDictionary = code => {
      return new Promise((resolve, reject) => {
        getDictionary(code)
          .then(res => {
            if (res !== false) {
              resolve(res.data.data.dictionaryoption);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };

    // 获取检测汇总信息
    const getReportSummaryInfo = currentinfo => {
      const param = {
        sampleId: currentinfo.sampleId,
        reportId: currentinfo.reportId,
        reportStage: currentinfo.reportStage
      };
      state.loadingStep2 = true;
      return new Promise((resolve, reject) => {
        getReportExpInfo(param)
          .then(res => {
            if (res !== false) {
              const experimentRequestList = res.data.data.data.experimentRequestList;
              const capabilityParaVos = res.data.data.data.capabilityParaVos;
              state.summarySelectJSON = {};
              state.summarySelectArray = [];
              res.data.data.data.reportStandardProductList.forEach(item => {
                state.summarySelectJSON[item.standardProductId] = item;
                state.summarySelectArray.push(item);
              });
              if (experimentRequestList && experimentRequestList.length > 0) {
                resolve(setExperimentData(experimentRequestList, capabilityParaVos));
              }
              if (capabilityParaVos && capabilityParaVos.length > 0) {
                setCapabilityParaVos(capabilityParaVos);
              }
            }
            state.loadingStep2 = false;
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    const handleCheckTemplate = item => {
      state.templateJsonData = item;
      const routeUrl = router.resolve({
        path: '/testReport/template',
        query: {
          type: 'check',
          experimentId: state.templateJsonData.id,
          samplesId: state.templateJsonData.sampleId,
          capabilityId: state.templateJsonData.sourceId
        }
      });
      window.open(
        routeUrl.href,
        '_blank',
        'height=1080, width=1080, top=100, toolbar=no, resizable=yes, location=no, status=no'
      );
    };

    function setExperimentData(experimentRequestList, capabilityParaVos) {
      const newexperimentRequestList = _.orderBy(experimentRequestList, ['order']);
      newexperimentRequestList.forEach((erl, index) => {
        const standardResultList = erl.standardResultList;
        erl.order = index;
        var fuceItems = [];
        if (capabilityParaVos && capabilityParaVos.length > 0) {
          const hasitem = _.filter(capabilityParaVos, cpv => {
            return erl.id === cpv.experimentId;
          });
          fuceItems = hasitem;
        }
        // 如果localList有值，说明该报告已经保存过了，这边就直接使用localList
        if (erl.localList && erl.localList.length > 0) {
          if (erl.standardResultList && erl.standardResultList.length === erl.localList.length) {
            erl.localList.forEach(item => {
              const standardIndex = erl.standardResultList.findIndex(
                standObj => standObj.customizedModel === item.name
              );
              item.smallnumber = standardIndex === -1 ? undefined : erl.standardResultList[standardIndex].smallnumber;
            });
          }
          erl.colorValue = [];
          var colorValueFlag1 = false;
          erl.localList.forEach((ll, indexv1) => {
            if (ll.expValues) {
              ll.value = JSON.parse(ll.expValues);
              ll.value.forEach(i => {
                if (i.paraValue && ll.smallnumber && ll.smallnumber.toString() !== '0' && ll.resultOptionType === 1) {
                  i.paraValue = getReservedSign(i.paraValue, ll.smallnumber); // 有效数位
                } else if (i.paraValue && ll.smallnumber && ll.smallnumber.toString() !== '0') {
                  // 约束小数位
                  i.paraValue = getFloatByNum(i.paraValue, ll.smallnumber);
                }
                if (i.colourValue && colorValueFlag1 === false) {
                  erl.colorValue.push(i.colourValue);
                }
              });
              if (erl.colorValue.length > 0) {
                colorValueFlag1 = true;
              }
            }
          });
          erl.childList = erl.localList;
        }
        // 如果localList为空，说明该报告第一次进来，则根据childList去匹配capabilityParaVos中的值
        if (erl.childList && erl.childList.length > 0 && erl.localList.length === 0) {
          erl.colorValue = [];
          var colorValueFlag = false;
          erl.childList.forEach((cl, indexv) => {
            if (standardResultList && standardResultList.length > 0) {
              const hasitem1 = _.filter(standardResultList, srl => {
                return cl.sourceId === srl.capabilityParaId;
              });
              if (hasitem1.length > 0) {
                cl = _.merge(cl, hasitem1[0]);
              }
            }
            if (capabilityParaVos && capabilityParaVos.length > 0) {
              const hasitem = _.filter(capabilityParaVos, cpv => {
                return cl.sourceId === cpv.capabilityParaId;
              });
              if (hasitem.length === 1) {
                if (hasitem[0].value && hasitem[0].value.length > 0) {
                  hasitem[0].value.forEach(i => {
                    if (
                      i.paraValue &&
                      cl.smallnumber &&
                      cl.smallnumber.toString() !== '0' &&
                      cl.resultOptionType === 1
                    ) {
                      // 有效数位
                      i.paraValue = getReservedSign(i.paraValue, cl.smallnumber);
                    } else if (i.paraValue && cl.smallnumber && cl.smallnumber.toString() !== '0') {
                      i.paraValue = getFloatByNum(i.paraValue, cl.smallnumber);
                    }
                    if (i.colourValue && colorValueFlag === false) {
                      erl.colorValue.push(i.colourValue);
                    }
                  });
                  if (erl.colorValue.length > 0) {
                    colorValueFlag = true;
                  }
                } else {
                  const para = [{ colourValue: '', paraKey: '', paraValue: '' }];
                  hasitem[0].value = para;
                }
                cl = _.merge(cl, hasitem[0]);
              } else if (hasitem.length > 1) {
                var fcItem = _.filter(fuceItems, fc => {
                  return cl.sourceId === fc.capabilityParaId;
                });
                if (fcItem.length > 0 && fcItem[0].value && fcItem[0].value.length > 0) {
                  fcItem[0].value.forEach(i => {
                    if (
                      i.paraValue &&
                      cl.smallnumber &&
                      cl.smallnumber.toString() !== '0' &&
                      cl.resultOptionType === 1
                    ) {
                      // 有效数位
                      i.paraValue = getReservedSign(i.paraValue, cl.smallnumber);
                    } else if (i.paraValue && cl.smallnumber && cl.smallnumber.toString() !== '0') {
                      i.paraValue = getFloatByNum(i.paraValue, cl.smallnumber);
                    }
                    if (i.colourValue && colorValueFlag === false) {
                      erl.colorValue.push(i.colourValue);
                    }
                  });
                  if (erl.colorValue.length > 0) {
                    colorValueFlag = true;
                  }
                } else {
                  const para = [{ colourValue: '', paraKey: '', paraValue: '' }];
                  fcItem = [];
                  fcItem.push({ value: para });
                }
                // fuceItems[0].expValues = fuceItems[0].value // JSON.stringify(fuceItems[0].value)
                cl = _.merge(cl, fcItem[0]);
              }
            }
          });
        }
      });
      return newexperimentRequestList;
    }

    function setCapabilityParaVos(capabilityParaVos) {
      // 获取所有实测值的列表，方便表达式过滤
      capabilityParaVos.forEach(capabilityPara => {
        if (capabilityPara.value && capabilityPara.value.length === 1) {
          state.allValues.push(capabilityPara.value[0]);
        } else if (capabilityPara.value.length > 1) {
          // 多线芯取最大值计算表达式
          const newList = _.orderBy(capabilityPara.value, ['paraValue']);
          state.allValues.push(newList[newList.length - 1]);
        }
      });
    }

    // 判定标准选择下拉框切换数据
    const getSelectsChanged = row => {
      const param = {
        standardProductId: row.standardProductId,
        standardProductVersion: row.standardProductVersion,
        sampleId: row.sampleId,
        sourceId: row.sourceId
      };
      getSelectChanged(param).then(res => {
        if (res !== false) {
          row.childList = JSON.parse(JSON.stringify(res.data.data));
          if (row.childList.length > 0) {
            row.childList.forEach(async cl => {
              cl.name = cl.customizedModel;
              if (cl.expValue === undefined || cl.expValue.length === 0) {
                cl.expValue = [{ colourValue: '', paraKey: '', paraValue: '' }];
              } else {
                cl.expValue.forEach(i => {
                  if (i.paraValue && cl.smallnumber && cl.smallnumber.toString() !== '0' && cl.resultOptionType === 1) {
                    // 有效小数位
                    i.paraValue = getReservedSign(i.paraValue, cl.smallnumber);
                  } else if (i.paraValue && cl.smallnumber && cl.smallnumber.toString() !== '0') {
                    i.paraValue = getFloatByNum(i.paraValue, cl.smallnumber);
                  }
                });
                cl.expValue.forEach(item => {
                  if (/^[+-]?[\d]+([\.][\d]+)?([Ee][+-]?[\d]+)?$/.test(item.paraValue)) {
                    const array = item.paraValue.toString().split('e');
                    if (array.length > 1) {
                      item.isZs = true;
                      item.exponentNumber = Number(array[1]);
                      item.baseNumber = Number(array[0]);
                    }
                  }
                });
              }
              cl.value = cl.expValue;
              if (cl.expResult === '' || cl.expResult === null || cl.expResult === undefined) {
                if (cl.isDetermine === true || cl.isDetermine === 'true') {
                  // 数值型判定
                  if (cl.resultType === '1') {
                    // 数值型分为标准型和非标准型 standardmathtype:非标准为'0'，标准为'1'
                    if (cl.standardmathtype === '1') {
                      cl.expResult = filterResult(cl.value, cl.maxNum, cl.minNum, cl.minSelected, cl.maxSeleced);
                    } else {
                      const capabilityJson = cl.capabilityjson ? JSON.parse(cl.capabilityjson) : [];
                      const javascriptMath = cl.javascriptmath ? cl.javascriptmath : '';
                      if (javascriptMath && capabilityJson) {
                        const resultObj = filterStanderMathResult(cl.value, capabilityJson, javascriptMath);
                        cl.expResult = resultObj.expResult;
                        cl.expRequirement = resultObj.expRequirement;
                      } else {
                        cl.expResult = '';
                        cl.expRequirement = '条件不足，无法解析';
                      }
                    }
                  } else if (cl.resultType === '2' || cl.resultType === '5') {
                    // 枚举型、自定义枚举判定
                    const dList =
                      cl.resultType === '2'
                        ? await getResultDictionary(cl.custlabel)
                        : filterOption(JSON.parse(cl.custlabel));
                    cl.expResult = filterMJResult(dList, cl.qualifiedOption, cl.noQualifiedOption, cl.value);
                  }
                } else if (cl.isDetermine === false || cl.isDetermine === 'false') {
                  cl.expResult = '不判定';
                } else {
                  cl.expResult = '';
                }
              }
            });
            judgmentItemResult(row.childList).then(res => {
              row.expResult = res;
            });
            changeSelectFlag(row);
          }
        }
      });
    };
    // 拖拽功能
    const rowDrop = () => {
      const tbody = document.querySelector('.left-main');
      Sortable.create(tbody, {
        handle: '.tes-move',
        draggable: '.tree-list',
        ghostClass: 'ghost',
        dragClass: 'drag',
        forceFallback: true,
        onStart(evt) {},
        onEnd({ newIndex, oldIndex }) {
          // 移除原来的数据
          const currRow = state.tableData.splice(oldIndex, 1)[0];
          // 移除原来的数据并插入新的数据
          state.tableData.splice(newIndex, 0, currRow);
          state.tableData.forEach((value, index) => {
            value.order = index;
          });
          state.asideKey += 1;
          context.emit('setInfo', state.tableData);
        }
      });
    };
    // js代码段解析
    const analysisJS = (javascriptMath, capabilityJson, values, expValue) => {
      // 创建标签
      // let script = document.createElement('script')
      // script.type = 'text/javascript'
      // script.text = ``
      // document.getElementsByTagName('head')[0].appendChild(script)
      // 模拟数据，后面有参数可以删除
      // javascriptMath =
      // capabilityJson = [{ key: 'value' }, { key: 'xyz' }]
      // values = [11, 12]
      // 获取表达式
      const func = javascriptMath; //
      const keys = [];
      values.push(expValue);
      if (capabilityJson.length > 0) {
        capabilityJson.forEach(json => {
          keys.push(json.templatekey);
        });
      }
      const Fn = new Function(keys.join(','), 'expValue', func);
      /**
       * 解析后返回一个对象
       * const param = {
       *   expResult: expResult, // 检测结论
       *   standardValue: standardValue // 计算的标准值
       *  }
       */
      return Fn(...values);
    };
    // 拖拽边框
    const widthChange = m => {
      state.asideWidth = state.asideWidth - m;
      if (state.asideWidth < state.asideMinWidth) {
        state.asideWidth = state.asideMinWidth;
      } else {
        if (state.asideWidth >= state.asideMaxWidth) {
          state.asideWidth = state.asideMaxWidth;
        }
      }
    };
    const setOldAsideWidth = () => {
      state.oldAsideWidth = JSON.parse(JSON.stringify(state.asideWidth));
    };
    const collapseLeft = () => {
      if (state.asideWidth === state.oldAsideWidth) {
        state.showAside = !state.showAside;
        state.collapseTip = state.showAside ? '点击折叠左面板' : '点击展开左面板';
      }
    };

    const changeParamSelectFlag = (row, parentRow) => {
      context.emit('setInfo', state.tableData);
    };
    const changeSelectFlag = row => {
      if (row.retestSourceId || row.isRetest === 1) {
        state.tableData.forEach(item => {
          if (item.sourceId === row.sourceId) {
            if (item.id === row.id) {
              item.childList.forEach(child => {
                child.selectFlag = row.selectFlag;
              });
            } else {
              item.selectFlag = row.selectFlag.toString() === '0' ? '1' : '0';
              item.childList.forEach(child => {
                child.selectFlag = row.selectFlag.toString() === '0' ? '1' : '0';
              });
            }
          }
        });
      } else {
        state.tableData.forEach(item => {
          if (item.id === row.id) {
            item.childList.forEach(child => {
              child.selectFlag = row.selectFlag;
            });
          }
        });
      }
      context.emit('setInfo', state.tableData);
    };
    // 修改了指数 baseNumber底数 exponentNumber 10的次幂数
    const changeZs = (baseNumber, exponentNumber, row, index, indexValue) => {
      if (!isNaN(Number(baseNumber))) {
        // 底数是数字类型才参与计算，否侧保持原样
        row.value[indexValue].paraValue = Number(baseNumber + 'e' + exponentNumber).toExponential();
        context.emit('setInfo', state.tableData);
      }
    };
    // 如果底数不合法则还原成原先的数据
    const handleBlurDs = (val, paraValue, indexValue, index, rowIndex) => {
      if (isNaN(Number(val))) {
        const oldNumber = state.tableData[index].childList[rowIndex].value[indexValue].paraValue
          .toString()
          .split('e')[0];
        state.tableData[index].childList[rowIndex].value[indexValue].baseNumber = oldNumber;
      }
    };

    watch(
      () => state.asideKey,
      (newValue, oldValue) => {
        nextTick(() => {
          rowDrop();
        });
      }
    );

    // #region 检测项目重置功能
    function handleReset() {
      ElMessageBox({
        title: '提示',
        message: `是否确认重置检测数据，重置后数据将不可恢复?`,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'info'
      })
        .then(() => {
          const resetParam = {
            sampleId: currentInfo.sampleId,
            reportId: currentInfo.reportId
          };
          state.loadingStep2 = true;
          resetReportCapability(resetParam)
            .then(res => {
              if (res !== false) {
                const experimentRequestList = res.data.data.data.experimentRequestList;
                const capabilityParaVos = res.data.data.data.capabilityParaVos;
                state.summarySelectJSON = {};
                state.summarySelectArray = [];
                res.data.data.data.reportStandardProductList.forEach(item => {
                  state.summarySelectJSON[item.standardProductId] = item;
                  state.summarySelectArray.push(item);
                });
                if (experimentRequestList && experimentRequestList.length > 0) {
                  const result = setExperimentData(experimentRequestList, capabilityParaVos);
                  state.tableData = result;
                  state.tableData.forEach(item => {
                    if (!state.summarySelectJSON[item.standardProductId]) {
                      item.standardProductId = state.summarySelectArray[0].standardProductId;
                      item.standardProduct = state.summarySelectArray[0].standardProductName;
                      item.standardProductVersion = state.summarySelectArray[0].standardProductVersion;
                      getSelectsChanged(item);
                    }
                  });
                }
                if (capabilityParaVos && capabilityParaVos.length > 0) {
                  setCapabilityParaVos(capabilityParaVos);
                }
              }
              state.loadingStep2 = false;
              filterSummaryList(false);
            })
            .catch();
          return true;
        })
        .catch(() => {
          return false;
        });
    }

    // #endregion

    return {
      ...toRefs(state),
      emptyImg,
      changeZs,
      rowClassName,
      getPermissionBtn,
      handleCheckTemplate,
      handleBlurDs,
      handleSelectionChange,
      formatDate,
      getNamesByIds,
      scrollJump,
      filterStatus,
      tableRef,
      drageHeader,
      getSelectsChanged,
      changeResult,
      changeTestResult,
      changeUnit,
      changeExp,
      changeMethod,
      changeItemResult,
      rowDrop,
      getResultDictionary,
      analysisJS,
      widthChange,
      setOldAsideWidth,
      collapseLeft,
      changeSelectFlag,
      changeParamSelectFlag,
      colWidth,
      getPlaceHolder,
      handleReset
    };
  }
};
</script>

<style lang="scss" scoped>
.edit-report-summary {
  margin-bottom: 60px;
  :deep(.el-table .hidden-row) {
    display: none;
  }
  :deep(.el-table .highlight-row) {
    background-color: #f56c6ca1 !important;
  }
  :deep(.el-input.is-disabled .el-input__inner) {
    background-color: #f5f7fa !important;
  }
  .exponent {
    width: 44px;
    position: absolute;
    top: -10px;
    left: 26px;
    :deep(.el-input__inner) {
      height: 20px;
      padding: 0 5px;
    }
  }
  .zsNumber {
    display: inline-block;
    position: relative;
    margin-left: 5px;
  }
  .el-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    background: $background-color;
    width: 100%;
  }
  .el-aside {
    background: $background-color;
    margin-bottom: 0;
    padding: 14px 20px 20px;
    p.title {
      text-align: left;
      margin: 0;
      font-size: 16px;
      font-weight: bold;
      padding-bottom: 5px;
      border-bottom: 1px solid #ebeef5;
    }
    .left-main {
      max-height: calc(100vh - 300px);
      overflow: auto;
    }
    .tree-list {
      display: flex;
      align-items: center;
      padding: 4px 0;
      text-align: left;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      &:hover {
        background: $tes-primary2;
      }
      &:active {
        background: $tes-primary2;
      }
      .line {
        display: inline-block;
        width: 4px;
        height: 16px;
        margin-right: 12px;
      }
      .custom-icon {
        width: 20px;
        height: 20px;
        font-size: 14px;
        margin-right: 12px;
      }
      .red-border {
        background: $red;
      }
      .green-border {
        background: $green;
      }
      .yellow-border {
        background: $yellow;
      }
      .text {
        font-size: 14px;
        max-width: calc(100% - 40px);
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }
    .ghost {
      background-color: #e6f8f4 !important;
    }
    .drag {
      background: #e6f8f4 !important;
      background-image: linear-gradient(#e9e9eb, #ffffff) !important;
    }
  }
  .close-handle {
    height: calc(100vh - 24rem);
    position: absolute;
    left: -24px;
  }
  .el-main {
    padding: 0 0 0 20px;
    overflow-y: hidden;
  }
  .main-content {
    background: $background-color;
    padding: 20px;
    &:not(:last-of-type) {
      margin-bottom: 20px;
    }
    .content-header {
      display: flex;
      width: 100%;
      height: 32px;
      align-items: center;
      &.flex-between {
        width: 100%;
        justify-content: space-between;
        margin-bottom: 16px;
      }
      .header-select {
        display: inline-block;
        width: 240px;
      }
      .title {
        font-size: 16px;
        font-weight: bold;
        color: $tes-primary;
        margin: 0 16px;
      }
      .label {
        color: $tes-font2;
      }
      :deep(.el-checkbox__input) {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .expanded {
      margin-bottom: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
      position: relative;
    }
    .marginBottom {
      margin-bottom: 8px;
    }
    .item-input {
      display: flex;
      .input-group {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.group:deep(.el-input-group__prepend) {
          padding: 0 4px;
          max-width: 60px;
          overflow: hidden;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &.group:deep(.el-input__inner) {
          padding: 0 7px;
          min-width: 40px;
        }
      }
    }
  }
}
</style>
