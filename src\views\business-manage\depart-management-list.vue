<template>
  <!-- 部门管理 -->
  <ListLayout
    :has-quick-query="false"
    :has-left-panel="true"
    :has-search-panel="false"
    :aside-panel-width="300"
    :aside-max-width="520"
    :has-button-group="getPermissionBtn('addDepartStaffBtn') ? true : false"
  >
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="condition"
          v-trim
          v-focus
          size="large"
          placeholder="请输入姓名/手机号"
          prefix-icon="el-icon-search"
          clearable
          @clear="getTableList()"
          @keyup.enter="getTableList()"
        />
        <el-button type="primary" size="large" style="margin-left: 10px" @click="getTableList()">查询</el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        size="large"
        type="primary"
        icon="el-icon-plus"
        @click="handleAddEdit()"
        @keyup.prevent
        @keydown.enter.prevent
        >新增员工</el-button
      >
    </template>
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <el-input v-model="filterText" size="small" placeholder="请输入部门名称" prefix-icon="el-icon-search" />
          <el-button
            v-if="getPermissionBtn('addDepartTreeBtn')"
            class="addTreeBtn"
            size="small"
            icon="el-icon-plus"
            @click="addTreeItem"
            @keyup.prevent
            @keydown.enter.prevent
          />
        </div>
        <div class="tree-content">
          <el-tree
            ref="treeRef"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            default-expand-all
            :expand-on-click-node="false"
            :highlight-current="true"
            draggable
            :filter-node-method="filterNode"
            :current-node-key="currentNodeKey"
            class="leftTree"
            :allow-drop="allowDrop"
            @filter="filterNode"
            @node-drop="nodeDrop"
            @node-click="clickNode"
          >
            <template #default="{ node, data }">
              <span>
                {{ node.label }}
                <span v-if="!node.data.status && data.id !== 'all'" class="red">(停用)</span>
              </span>
              <el-dropdown
                v-if="data.id !== 'all'"
                class="tree-dropdown el-icon"
                trigger="hover"
                :class="node.showIcon ? 'icon-show' : ''"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template
                  v-if="getPermissionBtn('editDepartTreeBtn') || getPermissionBtn('delDepartTreeBtn')"
                  #dropdown
                >
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="getPermissionBtn('editDepartTreeBtn')" @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="getPermissionBtn('delDepartTreeBtn')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="tableData"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
    >
      <el-table-column label="姓名" prop="index" :min-width="colWidth.people" show-overflow-tooltip>
        <template #default="{ row }">
          <div>
            <UserTag :name="row.nickname || ''" />
            <span v-if="row.status !== 3" :class="joinTypeClass[row.status]" class="joinType"
              >({{ joinType[row.status.toString()] }})</span
            >
            <span v-if="row.managerflag !== 0" class="manager">(系统管理员)</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="手机号" prop="mobile" :width="colWidth.phone">
        <template #default="{ row }">
          <span class="nowrap">{{ row.mobile || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="邮箱" prop="email" :width="colWidth.email" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.email || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="在职状态" prop="beOnTheJob" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ dictionaryAll['jobType'][row.beOnTheJob].name || row.beOnTheJob || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="部门" prop="deptNamePath" :width="colWidth.status">
        <template #default="{ row }">
          <span class="nowrap">{{ row.deptNamePath || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-if="
          getPermissionBtn('editDepartStaffBtn') ||
          getPermissionBtn('repwdDepartBtn') ||
          getPermissionBtn('resetInvite')
        "
        label="操作"
        prop="caozuo"
        :width="200"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <span v-if="getPermissionBtn('editDepartStaffBtn')" class="blue-color" @click="handleAddEdit(row)">查看</span>
          <span v-if="getPermissionBtn('repwdDepartBtn')" class="blue-color" @click="resetPassword(row)">密码重置</span>
          <span
            v-if="(row.status === 2 || row.status === 4) && getPermissionBtn('resetInvite')"
            class="blue-color"
            @click="resetInvite(row)"
            >重新邀请</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getTableList"
    />
    <!-- 添加树节点弹出框 -->
    <el-dialog
      v-model="departDialog"
      :title="isAddTree === true ? '新增部门' : '编辑部门'"
      width="480px"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="departDialog"
        ref="departFormRef"
        :model="departFormData"
        :rules="dialogRulesDepartment"
        size="small"
        label-position="right"
        :label-width="formLabelWidth"
      >
        <el-form-item label="状态：" prop="status">
          <el-radio-group v-model="departFormData.status" size="small">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="编号：" prop="code">
          <el-input v-model="departFormData.code" :disabled="!isAddTree" autocomplete="off" placeholder="请输入编号" />
        </el-form-item>
        <el-form-item
          label="部门名称："
          prop="name"
          :rules="{ required: true, message: '请输入部门名称', trigger: 'change' }"
        >
          <el-input v-model="departFormData.name" autocomplete="off" placeholder="请输入部门名称" maxlength="20" />
        </el-form-item>
        <el-form-item label="上级部门：" prop="parentId">
          <el-cascader
            v-model="departFormData.parentId"
            :options="dialogTreeData"
            :props="categoryProps"
            clearable
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="描述：" prop="content">
          <el-input
            v-model="departFormData.content"
            :rows="2"
            autocomplete="off"
            maxlength="300"
            type="textarea"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="small" @click="departDialog = false">取 消</el-button>
          <el-button type="primary" size="small" @click="submitDepartTree">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 新增、编辑员工弹出框 -->
    <DrawerDepart
      :drawer="employeesDrawer"
      :tree-node="treeNodeData"
      type="add"
      :dictionary="dictionaryAll"
      :tree-data="dialogTreeData"
      :detail-data="employeesData"
      @close="closeDrawer"
    />
    <DrawerDetail
      :drawer="drawerDetail"
      :dictionary="dictionaryAll"
      :user-id="rowId"
      :tree-data="dialogTreeData"
      @close="closeDrawer"
      @refresh="getTableList()"
    />
  </ListLayout>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance, nextTick } from 'vue';
import {
  getDepartTree,
  addDepartTree,
  editDepartTree,
  getMemberTable,
  saveEmployees,
  deleteDepartmentTree,
  updateEmployees,
  checkUserName,
  checkMobileURL,
  checkEmailURL,
  resetUserPassword,
  isDepartMentClose,
  updateOrderTree,
  alreadyExist
} from '@/api/departManagement';
import ListLayout from '@/components/ListLayout';
import { ElMessage, ElMessageBox } from 'element-plus';
import UserTag from '@/components/UserTag';
import { formatAllTree, formatTree } from '@/utils/formatJson';
import Pagination from '@/components/Pagination';
import { drageHeader } from '@/utils/formatTable';
import { getPermissionBtn } from '@/utils/common';
import { isMobile2, isEmail2, isAlphanumeric } from '@/utils/validate';
import { colWidth } from '@/data/tableStyle';
import DrawerDepart from './DrawerDepart';
import DrawerDetail from './DrawerDetail';
import { getDictionary } from '@/api/user';
import _ from 'lodash';
export default {
  name: 'DepartManagement',
  components: { Pagination, ListLayout, UserTag, DrawerDetail, DrawerDepart },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      asideWidth: 240,
      drawerDetail: false,
      treeRef: ref(),
      employeesData: {}, // 编辑员工信息
      employeesDrawer: false, // 新增编辑员工
      drawerDepartType: '', // 新增编辑员工弹出框类型
      rowId: '',
      departFormRef: ref(),
      dictionaryAll: {
        sex: {},
        highestDegree: {},
        jobType: {},
        JYLX: {}
      },
      formTree: ref(),
      tableData: [],
      dialogTreeData: [], // 弹出框中选择部门
      filterText: '',
      filterPMText: '',
      treeData: [],
      options: [], // 角色选项
      jobType: {
        1: '在职',
        0: '离职',
        2: '外部用户'
      },
      joinType: {
        1: '邀请中',
        2: '已拒绝',
        3: '已加入',
        4: '已退出'
      },
      joinTypeClass: {
        1: 'invitation',
        2: 'refused',
        3: '',
        4: 'clientExited '
      },
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      departFormData: {
        name: '',
        parentid: ''
      },
      formDataUser: {
        roles: []
      }, // 新增、编辑员工弹出框表单
      oldFormData: {}, // 编辑之前的员工信息
      condition: '', // 列表查询条件
      isAddTree: false,
      isAddEmployees: false, // 是否是新增员工
      showEdit: true,
      departDialog: false,
      employeesDialog: false,
      inputRef: ref(),
      showEditPMDialog: false,
      treeNodeData: {},
      isAddDialog: true,
      dialogRules: {
        mobile: [{ validator: isMobile2, tigger: 'change' }],
        email: [{ validator: isEmail2, tigger: 'change' }],
        companyEmail: [{ validator: isEmail2, tigger: 'change' }]
      },
      dialogRulesDepartment: {
        code: [
          { required: true, message: '请输入编号' },
          { validator: isAlphanumeric, tigger: 'change' }
        ]
      },
      formLabelWidth: '120px',
      roleList: [
        {
          label: '可选择',
          group: []
        },
        {
          label: '已停用',
          group: []
        }
      ], // 角色列表
      isDisable: true,
      showIcon: false,
      listQuery: {
        page: 1,
        limit: 20,
        categoryId: '',
        productModel: ''
      },
      treeLoading: false, // 部门树的loading
      tableLoading: false, // 表格的loading
      total: 0,
      currentNodeKey: ''
    });

    // 拖拽边框
    const widthChange = m => {
      state.asideWidth -= m;
      if (state.asideWidth <= 80) {
        state.asideWidth = 80;
      }
      if (state.asideWidth >= 600) {
        state.asideWidth = 600;
      }
    };
    const getDictionaryList = () => {
      Object.keys(state.dictionaryAll).forEach(dictionaryItem => {
        getDictionary(dictionaryItem).then(res => {
          if (res) {
            state.dictionaryAll[dictionaryItem] = {};
            res.data.data.dictionaryoption.forEach(val => {
              if (val.status === 1) {
                state.dictionaryAll[dictionaryItem][val.code] = val;
              }
            });
          }
        });
      });
    };
    getDictionaryList();
    // 过滤树节点
    watch(
      () => state.filterText,
      newValue => {
        state.treeRef.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    const loadNode = (node, resolve) => {
      if (!node.data.children || node.data.children.length === 0) {
        delete node.data['children'];
        node.data.leaf = true;
        // node.isLeaf = true
      }
      return resolve([]);
    };

    // 新增编辑
    const handleAddEdit = row => {
      if (state.treeData.length === 1) {
        proxy.$message.warning('请先添加部门！');
        return false;
      }
      if (row) {
        state.isAddEmployees = false;
        state.drawerDetail = true;
        state.rowId = row.id;
      } else {
        state.employeesDrawer = true;
        state.employeesData = {
          roles: [],
          beOnTheJob: 1,
          departmentId: state.treeNodeData.id === 'all' ? '' : state.treeNodeData.id
        };
      }
    };
    // 新增编辑抽屉关闭
    const closeDrawer = data => {
      state.drawerDetail = false;
      state.employeesDrawer = false;
      if (data.isRefresh) {
        getTableList();
      }
    };
    // 鼠标hover到树节点
    const mouseover = () => {
      state.showIcon = true;
    };
    const mouseleave = () => {
      state.showIcon = false;
    };
    // 新增树节点
    const addTreeItem = () => {
      state.departDialog = true;
      state.departFormData = {
        status: 1
      };
      state.isAddTree = true;
    };
    // 树节点编辑
    const editTree = (data, node) => {
      state.dialogTreeData = formatAllTree(data.id, state.dialogTreeData);
      state.departDialog = true;
      state.isAddTree = false;
      state.departFormData = JSON.parse(JSON.stringify(data));
    };
    // 树节点删除
    const delTree = node => {
      ElMessageBox({
        title: '提示',
        message: '是否删除该部门?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          if (node.id === state.treeNodeData.id) {
            // 如果删除的是当前部门
            // console.log(state.total)
            if (state.total) {
              proxy.$message.error('此部门下有成员，不允许删除');
            } else {
              deleteTree(node);
            }
          } else {
            state.tableLoading = true;
            getMemberTable({ departmentId: node.id, page: '10', limit: '20' }).then(res => {
              state.tableLoading = false;
              if (res) {
                if (res.data.data.totalCount) {
                  proxy.$message.error('此部门下有成员，不允许删除');
                } else {
                  deleteTree(node);
                }
              }
            });
          }
        })
        .catch(() => {});
    };
    const deleteTree = node => {
      state.tableLoading = true;
      deleteDepartmentTree(node.id).then(res => {
        state.tableLoading = false;
        if (res) {
          if (node.id === state.treeNodeData.id) {
            state.treeNodeData.id = '';
          }
          proxy.$message.success('删除成功');
          getDepartTreeList();
        }
      });
    };
    // 部门树列表
    const getDepartTreeList = () => {
      state.treeLoading = true;
      getDepartTree({}).then(function (res) {
        if (res) {
          state.treeLoading = false;
          const data = formatTree(res.data.data);
          state.treeData = JSON.parse(JSON.stringify(data));
          const all = { id: 'all', name: '全部' };
          state.treeData.unshift(all);
          if (!state.treeNodeData.id) {
            state.treeNodeData = state.treeData[0];
            getTableList();
          }
          if (state.treeNodeData.id) {
            nextTick(() => {
              state.treeRef.setCurrentKey(state.treeNodeData.id, true);
            });
          }
          state.dialogTreeData = formatAllTree('', data);
        }
      });
    };
    getDepartTreeList();
    // 员工列表
    const getTableList = query => {
      const params = {
        departmentId: state.treeNodeData.id === 'all' ? '' : state.treeNodeData.id,
        condition: state.condition
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      getMemberTable(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    // 保存员工
    const onSubmitEmployees = () => {
      proxy.$refs['formTree'].validate(valid => {
        if (valid) {
          var departmentId = state.formDataUser.departmentId;
          if (state.formDataUser.departmentId instanceof Array) {
            if (state.formDataUser.departmentId.length > 0) {
              departmentId = state.formDataUser.departmentId[state.formDataUser.departmentId.length - 1].toString();
            } else {
              departmentId = '';
            }
          }
          const params = {
            ...state.formDataUser,
            departmentId: departmentId
          };
          if (state.isAddEmployees) {
            checkUserName({ username: params.username }).then(res => {
              if (res.data.data === '0') {
                checkMobileEmail(params);
              } else if (res.data.data === '-1') {
                proxy.$message.error('该用户已有部门，请勿重复添加！');
              } else {
                handlExisting({ userId: res.data.data, ...params });
              }
            });
          } else {
            checkMobileEmail(params);
          }
        }
      });
    };
    // 已存在，是否邀请或加入
    const handlExisting = params => {
      ElMessageBox({
        title: '提示',
        message: '当前用户名已存在,<br>是否确认发送消息邀请' + state.formDataUser.nickname + '加入？',
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          alreadyExist(params).then(res => {
            if (res) {
              state.employeesDialog = false;
              proxy.$message.success('成功发送邀请信息');
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    const checkMobileEmail = params => {
      Promise.all([checkMobile(params.mobile), checkEmail(params.email)])
        .then(result => {
          if (result[0] && result[1]) {
            submitForm(params);
          }
        })
        .catch(error => {
          console.log(error);
        });
    };
    const checkMobile = value => {
      return new Promise((resolve, reject) => {
        // 输入了手机号，并且做了修改
        if (value && value !== state.oldFormData.mobile) {
          checkMobileURL({ mobile: value }).then(res => {
            if (res.data.data === 0) {
              resolve(true);
            } else {
              proxy.$message.error('手机号重复，请先修改');
              resolve(false);
            }
          });
        } else {
          resolve(true);
        }
      });
    };
    const checkEmail = value => {
      return new Promise((resolve, reject) => {
        // 输入了邮箱，并且做了修改
        if (value && value !== state.oldFormData.email) {
          checkEmailURL({ email: value }).then(res => {
            if (res.data.data === 0) {
              resolve(true);
            } else {
              proxy.$message.error('邮箱重复，请先修改');
              resolve(false);
            }
          });
        } else {
          resolve(true);
        }
      });
    };
    const submitForm = params => {
      if (state.isAddEmployees) {
        saveEmployees(params).then(res => {
          if (res) {
            state.employeesDialog = false;
            proxy.$message.success('员工添加成功！');
            getTableList();
          }
        });
      } else {
        updateEmployees(params).then(res => {
          if (res) {
            state.employeesDialog = false;
            proxy.$message.success('员工编辑成功！');
            getTableList();
          }
        });
      }
    };
    const resetPassword = row => {
      ElMessageBox({
        title: '提示',
        message: '密码将重置为初始密码：CX@lims<br>请提醒用户及时修改密码',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          state.tableLoading = true;
          resetUserPassword(row.userId).then(res => {
            state.tableLoading = false;
            if (res) {
              proxy.$message.success('密码重置成功！');
            }
          });
        })
        .catch(() => {});
    };
    // 提交部门树 编辑、新增
    const submitDepartTree = () => {
      proxy.$refs['departFormRef'].validate(valid => {
        if (valid) {
          var parentId = state.departFormData.parentId;
          if (state.departFormData.parentId instanceof Array) {
            if (state.departFormData.parentId.length > 0) {
              parentId = state.departFormData.parentId[state.departFormData.parentId.length - 1].toString();
            } else {
              parentId = '';
            }
          }
          const params = {
            ...state.departFormData,
            parentId: parentId || '0'
          };
          if (state.isAddTree) {
            state.treeLoading = true;
            addDepartTree(params).then(function (res) {
              state.treeLoading = false;
              if (res) {
                proxy.$message.success('添加部门成功!');
                state.departDialog = false;
                getDepartTreeList();
              }
            });
          } else {
            if (params.status === 0) {
              state.treeLoading = true;
              isDepartMentClose(params.id).then(res => {
                state.treeLoading = false;
                if (res) {
                  if (res.data.data) {
                    submitEditTree(params);
                  } else {
                    proxy.$message.error('该部门不可停用!');
                  }
                }
              });
            } else {
              submitEditTree(params);
            }
          }
        } else {
          return false;
        }
      });
    };
    const submitEditTree = params => {
      state.treeLoading = true;
      editDepartTree(params).then(function (res) {
        state.treeLoading = false;
        if (res) {
          proxy.$message.success('修改部门成功!');
          state.departDialog = false;
          getDepartTreeList();
        }
      });
    };
    // 点击树节点
    const clickNode = node => {
      state.treeNodeData = node;
      getTableList();
    };
    // 保存树节点
    const editDialogSuccess = () => {
      state.formTree.validate(valid => {
        if (valid) {
          if (state.isAddTree !== true) {
            editDepartTree(state.departFormData).then(function (res) {
              if (res !== false && res.data.code === 200) {
                ElMessage.success('编辑成功!');
                state.departDialog = false;
                getDepartTreeList();
              }
            });
          } else {
            addDepartTree(state.departFormData).then(function (res) {
              if (res !== false && res.data.code === 200) {
                ElMessage.success('新增成功!');
                state.departDialog = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 查询重置
    const resetSearch = () => {
      state.condition = '';
      getTableList();
    };
    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      if (draggingNode.level === dropNode.level) {
        // parentid是父节点id
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    const resetInvite = row => {
      ElMessageBox({
        title: '提示',
        message: '是否确认重新邀请',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          state.tableLoading = true;
          alreadyExist(row).then(res => {
            state.tableLoading = false;
            if (res) {
              proxy.$message.success('成功发送邀请信息');
              getTableList();
            }
          });
        })
        .catch(() => {});
    };
    // 树排序
    const nodeDrop = (before, after) => {
      // console.log(after)
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === 'all';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      updateOrderTree(orderList).then(res => {
        if (res !== false) {
          getDepartTreeList();
          proxy.$message.success('排序成功');
        }
      });
    };

    return {
      ...toRefs(state),
      closeDrawer,
      getTableList,
      resetInvite,
      handlExisting,
      allowDrop,
      nodeDrop,
      submitForm,
      checkMobile,
      checkEmail,
      resetPassword,
      checkMobileEmail,
      onSubmitEmployees,
      getPermissionBtn,
      submitDepartTree,
      clickNode,
      delTree,
      deleteTree,
      resetSearch,
      getDepartTreeList,
      handleAddEdit,
      widthChange,
      filterNode,
      addTreeItem,
      editDialogSuccess,
      editTree,
      submitEditTree,
      mouseover,
      mouseleave,
      loadNode,
      changeIcon,
      drageHeader,
      colWidth
    };
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}

.red {
  color: $red;
  font-size: 12px;
}
.color-red {
  color: $red;
}

.joinType {
  display: inline-block;
  margin-left: 5px;
  font-size: 12px;
}
.invitation {
  color: $tes-primary;
}
.refused {
  color: $tes-tag-important;
}
.clientExited {
  color: #909399;
}
.manager {
  font-size: 12px;
  color: $tes-tag-important;
}
.el-select--medium {
  width: 100%;
}
</style>
