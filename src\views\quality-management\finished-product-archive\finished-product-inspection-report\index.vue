<template>
  <div class="h-full flex flex-col overflow-hidden">
    <div class="flex justify-end mb-4">
      <el-button type="primary" @keyup.prevent @keydown.enter.prevent @click="onBatchImportRecord()"
        >批量导出报告</el-button
      >
      <el-button type="primary" @keyup.prevent @keydown.enter.prevent @click="onExportRawRecord()">
        导出原始记录</el-button
      >
    </div>
    <div class="flex flex-col overflow-hidden">
      <div class="flex-1 overflow-hidden flex flex-col">
        <el-table
          ref="tableRef"
          key="id"
          v-loading="state.loading"
          :data="state.list"
          fit
          border
          size="medium"
          highlight-current-row
          class="dark-table format-height-table base-table h-full"
          :row-style="
            () => {
              return 'cursor: pointer';
            }
          "
          @header-dragend="drageHeader"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" fixed="left" prop="checkbox" :width="colWidth.checkbox" align="center" />
          <el-table-column
            v-for="item in columns"
            :key="item.field"
            :label="item.title"
            :prop="item.field"
            :min-width="colWidth.orderNo"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <template v-if="item.field === 'latestArchivalReportEntityReportNo'">
                {{ row.latestArchivalReportEntity?.reportNo }}
              </template>
              <template v-else-if="item.field === 'type'">
                {{ InspectionTypeConstMapDesc[row.type] }}
              </template>
              <template v-else-if="item.field === 'inboundLengthInputWarehouseUnit'">
                {{ row.inboundLength
                }}{{ row.inputWarehouseUnit ? state.dictionary['5'].all[row.inputWarehouseUnit] : '--' }}
              </template>
              <template v-else-if="item.field === 'latestArchivalReportEntityFileUrl'">
                <div class="cursor-pointer text-primary">
                  {{
                    row.latestArchivalReportEntity?.reportNo ? `${row.latestArchivalReportEntity?.reportNo}.pdf` : ''
                  }}
                </div>
              </template>
              <div v-else>{{ row[item.field] || '--' }}</div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :page="state.pagination.page"
          :limit="state.pagination.limit"
          :total="state.total"
          @pagination="handleQuery"
        />
      </div>
      <div class="mt-4 flex-1">
        <div class="font-semibold mb-4 text-left">同销售订单下其他成品抽检报告</div>
        <SameSaleOrderInspectionReport :params="sameSaleOrderInspectionReportParams" />
      </div>
    </div>
  </div>
</template>

<script setup>
import Pagination from '@/components/Pagination';
import { colWidth } from '@/data/tableStyle';
import { drageHeader } from '@/utils/formatTable';
import { onMounted, reactive, ref, computed } from 'vue';
import { columns } from './column';
import { querySampleArchival } from '@/api/order-sample-archival';
import SameSaleOrderInspectionReport from './same-sale-order-inspection-report/index.vue';
import { InspectionTypeConst, InspectionTypeConstMapDesc } from '@/const/inspection-type-const';
import { getDictionary } from '@/api/user';

const emits = defineEmits(['onGetNotFoundBatchNoList']);
defineExpose({ query });

const state = reactive({
  selected: [],
  list: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20
  },
  total: 0,
  dictionary: {
    5: {
      enable: {},
      all: {}
    }
  },
  sameSaleOrderInspectionReportParams: [
    {
      salesOrderNo: '',
      salesItemNo: ''
    }
  ]
});

const tableRef = ref();

onMounted(() => {
  handleQuery();
  getDictionaryList();
});

const sameSaleOrderInspectionReportParams = computed(() => {
  return {
    sampleType: InspectionTypeConst.FINISHED_PRODUCT_SAMPLING,
    salesOrderRequestList: state.list?.map(x => ({
      salesOrderNo: x.salesOrderNo,
      salesItemNo: x.salesItemNo
    }))
  };
});

const getDictionaryList = () => {
  Object.keys(state.dictionary).forEach(async item => {
    const response = await getDictionary(item);
    if (response) {
      state.dictionary[item] = { enable: {}, all: {} };
      response.data.data.dictionaryoption.forEach(optionItem => {
        if (optionItem.status == 1) {
          state.dictionary[item].enable[optionItem.code] = optionItem.name;
        }
        state.dictionary[item].all[optionItem.code] = optionItem.name;
      });
    }
  });
};

const onBatchImportRecord = () => {
  //
};

const onExportRawRecord = () => {
  //
};

const handleSelectionChange = val => {
  state.selected = val;
};

const handleRowClick = row => {
  state.tableRef.toggleRowSelection(
    row,
    !state.selected.some(item => {
      return row.sampleId === item.sampleId;
    })
  );
};

async function query(params = {}) {
  state.pagination.page = 1;
  state.pagination.limit = 20;
  handleQuery({ ...params, ...state.pagination });
}

const handleQuery = async (params = {}) => {
  try {
    //  state.loading = true;
    const { data } = await querySampleArchival({
      ...params,
      sampleType: InspectionTypeConst.FINISHED_PRODUCT_SAMPLING
    });

    const { notFoundBatchNoList, pageUtils: { currPage, pageSize, totalCount, list } = {} } = data.data;
    state.pagination.limit = pageSize;
    state.pagination.page = currPage;
    state.total = totalCount;
    state.list = list;
    emits('onGetNotFoundBatchNoList', notFoundBatchNoList);
  } catch (error) {
    console.error('Failed to fetch data:', error);
  } finally {
    state.loading = false;
  }
};
</script>

<style lang="scss" scoped></style>
