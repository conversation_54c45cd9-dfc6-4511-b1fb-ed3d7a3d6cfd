<template>
  <el-dialog
    v-model="modelValue"
    title="批量查询"
    destroy-on-close
    width="40%"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @closed="onClosed()"
  >
    <div>
      <div class="mb-4">
        <span class="text-base mb-2">请选择批量查询字段：</span>
        <el-select v-model="state.field" class="!w-full" placeholder="请选择批量查询字段" clearable>
          <el-option v-for="item in fileds" :key="item.filed" :label="item.label" :value="item.filed" />
        </el-select>
      </div>
      <div>
        <div class="mb-4">
          <span class="text-base">请输入需要查询的数据：</span>
          <span class="text-red-500 text-base">系统按照数据完全匹配，请勿简写</span>
        </div>
        <div>
          <el-input
            v-model="state.value"
            :autosize="{ minRows: 20, maxRows: 20 }"
            type="textarea"
            resize="none"
            placeholder="示例：
BC2025080988
BC2025080989"
          />
          <span class="text-red-500 text-xs mt-4">多份数据，请换行输入。</span>
        </div>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="onClosed()">取消</el-button>
        <el-button type="primary" @click="onConfirm()">确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { reactive } from 'vue';
import { computed } from 'vue';

const fileds = [
  { filed: 'batchNo', label: '公司批号', alias: 'batchNoList' },
  { filed: 'projectName', label: '工程项目名称', alias: 'projectNameList' },
  { filed: 'salesOrderNo', label: '销售订单', alias: 'salesOrderNoList' },
  { filed: 'productionOrderNo', label: '生产订单', alias: 'productionOrderNoList' },
  { filed: 'materialNo', label: '物料编号', alias: 'materialNoList' },
  { filed: 'materialDesc', label: '物料描述', alias: 'materialDescList' }
];

const emits = defineEmits(['update:modelValue', 'onQuery']);

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const state = reactive({
  field: '',
  value: ''
});

const modelValue = computed({
  get() {
    return props.modelValue;
  },
  set(val) {
    emits('update:modelValue', val);
  }
});

const onClosed = () => {
  modelValue.value = false;
};

const onConfirm = () => {
  if (state.field) {
    if (!state.value?.trim()) {
      ElMessage.warning('查询数据不能为空');
      return;
    }
  }

  if (state.value?.trim()) {
    if (!state.field) {
      ElMessage.warning('查找字段不能为空');
      return;
    }
  }

  const batchNoList = state.value?.split(/[,，\n]+/).filter(Boolean);
  const field = fileds.find(item => item.filed === state.field)?.alias;
  emits('onQuery', { [field]: batchNoList });
  modelValue.value = false;
};
</script>
<style lang="scss" scoped></style>
