<template>
  <div class="h-full flex flex-col">
    <el-table
      ref="tableRef"
      key="id"
      v-loading="state.loading"
      :data="state.list"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column type="selection" fixed="left" prop="checkbox" :width="colWidth.checkbox" align="center" />
      <el-table-column
        v-for="item in columns"
        :key="item.field"
        :label="item.title"
        :prop="item.field"
        :min-width="colWidth.orderNo"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <template v-if="item.field === 'latestArchivalReportEntityReportNo'">
            {{ row.latestArchivalReportEntity?.reportNo }}
          </template>
          <template v-else-if="item.field === 'type'">
            {{ InspectionTypeConstMapDesc[row.type] }}
          </template>
          <template v-else-if="item.field === 'inboundLengthInputWarehouseUnit'">
            {{ row.inboundLength
            }}{{ row.inputWarehouseUnit ? state.dictionary['5']?.all[row.inputWarehouseUnit] : '--' }}
          </template>
          <template v-else-if="item.field === 'latestArchivalReportEntityFileUrl'">
            <div class="cursor-pointer text-primary">
              {{ row.latestArchivalReportEntity?.reportNo ? `${row.latestArchivalReportEntity?.reportNo}.pdf` : '' }}
            </div>
          </template>
          <div v-else>{{ row[item.field] || '--' }}</div>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :page="state.pagination.page"
      :limit="state.pagination.limit"
      :total="state.total"
      @pagination="handleQuery"
    />
  </div>
</template>

<script setup>
import Pagination from '@/components/Pagination';
import { colWidth } from '@/data/tableStyle';
import { drageHeader } from '@/utils/formatTable';
import { onMounted, reactive, ref, watch } from 'vue';
import { columns } from './column';
import { getDictionary } from '@/api/user';
import { querySampleArchival } from '@/api/order-sample-archival';
import { InspectionTypeConstMapDesc } from '@/const/inspection-type-const';

const props = defineProps({
  params: {
    type: Object,
    required: false,
    default: () => {}
  }
});

const state = reactive({
  selected: [],
  list: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 10
  },
  total: 0,
  dictionary: {
    5: {
      enable: {},
      all: {}
    }
  }
});

const tableRef = ref();

watch(
  () => props.params,
  params => {
    if (!params || Object.keys(params).length === 0) {
      state.list = [];
    } else {
      handleQuery();
    }
  },
  {
    deep: true
  }
);

onMounted(() => {
  getDictionaryList();
});

const getDictionaryList = () => {
  Object.keys(state.dictionary).forEach(async item => {
    const response = await getDictionary(item);
    if (response) {
      state.dictionary[item] = { enable: {}, all: {} };
      response.data.data.dictionaryoption.forEach(optionItem => {
        if (optionItem.status == 1) {
          state.dictionary[item].enable[optionItem.code] = optionItem.name;
        }
        state.dictionary[item].all[optionItem.code] = optionItem.name;
      });
    }
  });
};

const handleSelectionChange = val => {
  state.selected = val;
};

const handleRowClick = row => {
  state.tableRef.toggleRowSelection(
    row,
    !state.selected.some(item => {
      return row.sampleId === item.sampleId;
    })
  );
};

const handleQuery = async () => {
  try {
    // state.loading = true;
    const { data } = await querySampleArchival(props.params);
    const { pageUtils: { currPage, pageSize, totalCount, list } = {} } = data.data;
    state.pagination.limit = pageSize;
    state.pagination.page = currPage;
    state.total = totalCount;
    state.list = list;
  } catch (error) {
    console.error('Failed to fetch data:', error);
  } finally {
    state.loading = false;
  }
};
</script>

<style lang="scss" scoped></style>
