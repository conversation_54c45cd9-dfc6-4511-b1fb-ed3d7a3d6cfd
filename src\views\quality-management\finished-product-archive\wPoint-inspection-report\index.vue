<template>
  <div class="flex justify-end mb-4">
    <el-button
      :loading="state.exportReportLoading"
      type="primary"
      @keyup.prevent
      @keydown.enter.prevent
      @click="onExportReport()"
      >批量导出报告</el-button
    >
    <el-button
      type="primary"
      :loading="state.exportRawRecordLoading"
      @keyup.prevent
      @keydown.enter.prevent
      @click="onExportRawRecord()"
    >
      导出原始记录</el-button
    >
  </div>
  <el-table
    ref="tableRef"
    key="id"
    v-loading="state.loading"
    :data="state.list"
    fit
    border
    height="auto"
    size="medium"
    highlight-current-row
    class="dark-table format-height-table base-table"
    :row-style="
      () => {
        return 'cursor: pointer';
      }
    "
    @header-dragend="drageHeader"
    @selection-change="handleSelectionChange"
    @row-click="handleRowClick"
  >
    <el-table-column type="selection" fixed="left" prop="checkbox" :width="colWidth.checkbox" align="center" />
    <el-table-column
      v-for="item in columns"
      :key="item.field"
      :label="item.title"
      :prop="item.field"
      :min-width="colWidth.orderNo"
      show-overflow-tooltip
    >
      <template #default="{ row }">
        <template v-if="item.field === 'latestArchivalReportEntityReportNo'">
          {{ row.latestArchivalReportEntity?.reportNo }}
        </template>
        <template v-else-if="item.field === 'type'">
          {{ InspectionTypeConstMapDesc[row.type] }}
        </template>
        <template v-else-if="item.field === 'inboundLengthInputWarehouseUnit'">
          {{ row.inboundLength }}{{ row.inputWarehouseUnit ? state.dictionary['5'].all[row.inputWarehouseUnit] : '--' }}
        </template>
        <template v-else-if="item.field === 'latestArchivalReportEntityFileUrl'">
          <div class="cursor-pointer text-primary">
            {{ row.latestArchivalReportEntity?.reportNo ? `${row.latestArchivalReportEntity?.reportNo}.pdf` : '' }}
          </div>
        </template>
        <div v-else>{{ row[item.field] || '--' }}</div>
      </template>
    </el-table-column>
  </el-table>
  <pagination
    :page="state.pagination.page"
    :limit="state.pagination.limit"
    :total="state.total"
    @pagination="handleQuery"
  />
</template>

<script setup>
import Pagination from '@/components/Pagination';
import { colWidth } from '@/data/tableStyle';
import { drageHeader } from '@/utils/formatTable';
import { columns } from './column';
import { reactive, ref, onMounted } from 'vue';
import { getDictionary } from '@/api/user';
import { querySampleArchival, downloadZip } from '@/api/order-sample-archival';
import { InspectionTypeConst, InspectionTypeConstMapDesc } from '@/const/inspection-type-const';
import { ElMessage } from 'element-plus';
import { saveAs } from 'file-saver';

const emits = defineEmits(['onGetNotFoundBatchNoList']);
defineExpose({ query });

const state = reactive({
  selected: [],
  list: [],
  loading: false,
  pagination: {
    page: 1,
    limit: 20
  },
  total: 0,
  dictionary: {
    5: {
      enable: {},
      all: {}
    }
  },
  exportRawRecordLoading: false,
  exportReportLoading: false
});

const tableRef = ref();

onMounted(() => {
  handleQuery(state.pagination);
  getDictionaryList();
});

const getDictionaryList = () => {
  Object.keys(state.dictionary).forEach(async item => {
    const response = await getDictionary(item);
    if (response) {
      state.dictionary[item] = { enable: {}, all: {} };
      response.data.data.dictionaryoption.forEach(optionItem => {
        if (optionItem.status == 1) {
          state.dictionary[item].enable[optionItem.code] = optionItem.name;
        }
        state.dictionary[item].all[optionItem.code] = optionItem.name;
      });
    }
  });
};

async function query(params = {}) {
  state.pagination.page = 1;
  state.pagination.limit = 20;
  handleQuery({ ...params, ...state.pagination });
}

const handleSelectionChange = val => {
  state.selected = val;
};

const onExportReport = async () => {
  if (!Array.isArray(state.selected) || state.selected.length === 0) {
    ElMessage.warning('请选择导出报告');
    return;
  }

  const urls = state.selected.map(item => item.latestArchivalReportEntity?.fileUrl).filter(Boolean);
  if (!Array.isArray(urls) || urls.length === 0) {
    ElMessage.warning('导出报告文件地址和为空');
    return;
  }

  state.exportReportLoading = true;
  const { data } = await downloadZip(urls);
  saveAs(data, '导出报告.zip');
  state.exportReportLoading = false;
  ElMessage.success('下载成功');
};

const onExportRawRecord = async () => {
  if (!Array.isArray(state.selected) || state.selected.length === 0) {
    ElMessage.warning('请选择导出原始记录');
    return;
  }

  const urls = state.selected
    .flatMap(item => item.archivalExperimentEntityList.map(entity => entity.fileUrl))
    .filter(Boolean);

  if (!Array.isArray(urls) || urls.length === 0) {
    ElMessage.warning('导出原始记录文件地址和为空');
    return;
  }
  state.exportRawRecordLoading = true;
  const { data } = await downloadZip(urls);
  saveAs(data, '原始记录.zip');
  state.exportRawRecordLoading = false;
  ElMessage.success('下载成功');
};

const handleRowClick = row => {
  state.tableRef.toggleRowSelection(
    row,
    !state.selected.some(item => {
      return row.sampleId === item.sampleId;
    })
  );
};

const handleQuery = async (params = {}) => {
  state.loading = true;
  const { data } = await querySampleArchival({
    ...params,
    sampleType: InspectionTypeConst.W_POINT_SAMPLING
  }).finally(() => (state.loading = false));
  const { notFoundBatchNoList, pageUtils: { currPage, pageSize, totalCount, list } = {} } = data.data;
  state.pagination.limit = pageSize;
  state.pagination.page = currPage;
  state.total = totalCount;
  state.list = list;
  emits('onGetNotFoundBatchNoList', notFoundBatchNoList);
};
</script>

<style lang="scss" scoped></style>
