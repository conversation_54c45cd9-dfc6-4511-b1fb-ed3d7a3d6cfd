<template>
  <!-- 样品进度报表 -->
  <ListLayout v-loading="expLoading">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="key">
          <el-input
            v-model="formInline.key"
            v-trim
            v-focus
            placeholder="请输入编号/样品名称/型号规格"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="
              getTableList();
              getTypeNumber();
            "
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button
            type="primary"
            size="large"
            @click="
              getTableList();
              getTypeNumber();
            "
            >查询</el-button
          >
          <el-button size="large" @click="reset">重置</el-button>
          <el-button
            class="searchBtn"
            size="large"
            type="text"
            @click="advancedSearch"
            @keyup.prevent
            @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('gistAdd')"
        type="primary"
        size="large"
        @click="handleExport"
        @keyup.prevent
        @keydown.enter.prevent
        ><span class="iconfont tes-task-issued" /> 导出</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchFromRef" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="物资分类：" prop="mateCode">
              <el-radio-group v-model="searchForm.mateCode" size="small" @change="handleChangeMate">
                <el-radio-button label="">不限</el-radio-button>
                <el-radio-button v-for="type in tabsData" :key="type" :label="type.code" class="label-type">
                  {{ type.name }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="登记时间：" props="registerTime">
              <el-date-picker
                v-model="searchForm.registerTime"
                type="date"
                placeholder="请选择登记时间"
                @change="handleSubmitTime"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="status" size="small" style="float: left" @change="changeStatus">
        <el-radio-button label="-1"
          >全部<span v-if="totalNumber['-1']">({{ totalNumber['-1'] }})</span></el-radio-button
        >
        <el-radio-button v-for="(value, key, index) in statusList" :key="index" :label="key"
          >{{ value }}
          <span v-if="totalNumber[key]">({{ totalNumber[key] }})</span>
        </el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table"
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="序号" width="80px" align="center">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="样品编号" prop="secSampleNum" width="180px" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-if="row.secSampleNum" v-copy="row.secSampleNum" class="blue-color" @click="handleTz(row, 'yp')">
            {{ row.secSampleNum }}
          </div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="样品进度" prop="status" width="200" show-overflow-tooltip>
        <template #default="{ row }">
          <div :class="{ isShowHand: row.status == 2 }">
            <!-- 0、未下达；1、未分配； 2、检测中； 3、报告中；4、检测完成 -->
            <el-popover
              placement="right"
              :disabled="row.status != 2"
              popper-class="schedulePopover"
              :width="40"
              trigger="click"
              @show="checkRowNumber(row)"
            >
              <template #reference>
                <el-progress
                  v-if="row.status || row.status == 0"
                  :percentage="percentage[row.status]"
                  :status="row.status == 4 ? 'success' : 'warning'"
                >
                  <span>{{ statusList[row.status] }}</span>
                </el-progress>
                <span v-else>--</span>
              </template>
              <div class="icon-tes-info">待提交 {{ rowNumberDetail.unSubmit }}</div>
              <div class="icon-tes-wait">待审核 {{ rowNumberDetail.unCheck }}</div>
              <div class="icon-tes-success">已通过 {{ rowNumberDetail.complete }}</div>
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="sampleName" min-width="280px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.sampleName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="prodType" min-width="150px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.prodType || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="物料编号" prop="materialNo" width="150px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.materialNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="批次" prop="batchNo" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.batchNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="盘号" prop="reelNo" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.reelNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验对象" prop="productionOrderNo" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.type === 1">{{ row.inputWarehouseNo || '--' }}</span>
          <span v-if="row.type !== 1">{{ row.productionOrderNo || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对象位置" prop="wareHouseName" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.type == 1">{{ row.wareHouseName || '--' }}</span>
          <span v-else-if="row.type !== 1 && (row.productionProcedure || row.productionStation)"
            >{{ row.productionProcedure + ' ' + row.productionStation }}
          </span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="对象名称" prop="supplierName" min-width="120px" show-overflow-tooltip>
        <template #default="{ row }">
          <span v-if="row.type == 1">{{ row.supplierName || '--' }}</span>
          <span v-if="row.type !== 1">{{ row.customerName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试验负责人" prop="ownerId" min-width="120px">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.ownerId) || row.ownerId || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="流程轨迹" fixed="right" width="100px" class-name="fixed-right">
        <template #default="{ row }">
          <span class="blue-color" @click="handleCheck(row)">查看</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    <template #other>
      <!-- 详情抽屉 -->
      <el-drawer v-model="detailDrawer" title="流程轨迹" direction="rtl" size="30%" custom-class="scheduleDrawer">
        <div v-loading="drawerLoading" class="timeLineContent">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in processList"
              :key="index"
              size="large"
              :class="{
                isCurrentNode: item.isCurrentNode && index !== processList.length - 1,
                hadEndTime: item.isHaved
              }"
            >
              <div
                v-if="
                  (!item.experimentData || item.experimentData.length == 0) &&
                  (!item.reportData || item.reportData.length == 0)
                "
                class="timeLineTop"
              >
                <div class="nodeName inlineBlock">{{ item.nodeName }}</div>
                <div class="endTime inlineBlock">{{ item.endTime }}</div>
                <div style="font-size: 14px; margin-top: 8px">{{ getNameByid(item.executorId) }}</div>
              </div>
              <el-collapse v-if="item.experimentData && item.experimentData.length > 0" v-model="activeCollapse">
                <el-collapse-item :title="item.nodeName" name="1">
                  <el-table
                    :data="item.experimentData"
                    :show-header="false"
                    class="dark-table base-table collapseTable"
                    fit
                    style="width: 100%"
                  >
                    <el-table-column prop="realOwnerId" :min-width="160" show-overflow-tooltip>
                      <template #default="{ row }">
                        <div :class="row.status == 2 ? 'icon-tes-wait' : 'icon-tes-success'">
                          {{ getNamesByid(row.realOwnerId).toString() || '--' }}
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="capabilityName" :min-width="220" show-overflow-tooltip>
                      <template #default="{ row }">
                        <div class="item">
                          <span v-if="row.retestSourceId" class="custom-icon text-copy">复</span>
                          <span v-if="row.isRetest" class="custom-icon text-origin">源</span>
                          <span v-if="row.isEditMore" class="custom-icon text-change">改</span>
                          <span v-if="row.isBack" class="custom-icon text-back">退</span>
                          <span>{{ row.capabilityName }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="submitDate" :width="160" show-overflow-tooltip fixed="right" />
                  </el-table>
                </el-collapse-item>
              </el-collapse>
              <el-collapse v-if="item.reportData && item.reportData.length > 0" v-model="activeCollapse2">
                <el-collapse-item :title="item.nodeName" name="2">
                  <div v-for="val in item.reportData" :key="val.reportFormationDateTime">
                    <el-table
                      v-if="val.processData.length > 0"
                      :data="val.processData"
                      class="dark-table base-table collapseTable"
                      :show-header="false"
                      fit
                      border
                    >
                      <el-table-column prop="executorId" show-overflow-tooltip>
                        <template #default="{ row }">
                          <span>{{ getNamesByid(row.executorId).toString() || '--' }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column prop="endTime" :width="160" fixed="right" show-overflow-tooltip />
                    </el-table>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, onMounted, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import router from '@/router/index.js';
import { getList, getNumber, getLine, getRowNumber, getExport } from '@/api/sampleSchedule';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { useStore } from 'vuex';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { getDictionary } from '@/api/user';
import ListLayout from '@/components/ListLayout';
import UserTag from '@/components/UserTag';

export default {
  name: 'SampleSchedule',
  components: { Pagination, ListLayout, UserTag },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const state = reactive({
      tableData: [],
      formInline: {
        key: ''
      },
      editFrom: ref(0),
      total: 0,
      processList: [], // 流程轨迹
      activeCollapse: '',
      searchForm: {
        mateCode: ''
      },
      searchFromRef: ref(),
      showS: false,
      activeName: '0',
      activeCollapse2: '',
      expLoading: false, // 导出的loading
      drawerLoading: false, // 流程轨迹loading
      status: '-1', // 查询
      detailDrawer: false, // 流程轨迹抽屉
      tableLoading: false, // 表格加载的loading
      isShowProduct: false, // 产品弹出框
      exportData: [], // 需要导出的数据
      rowNumberDetail: {},
      dialogFrom: {}, // 操作树节点的弹窗表格
      checkTreeId: '', // 选中的左侧树节点的id
      listQuery: {
        page: 1,
        limit: 20
      },
      totalNumber: {
        '-1': '',
        0: '',
        1: '',
        2: '',
        3: '',
        4: ''
      },
      activeIndex: '0',
      activeMoreIndex: null,
      tabsData: store.state.user.materialList,
      tabsMoreData: [],
      statusList: {
        0: '未下达',
        1: '未分配',
        2: '检测中',
        3: '报告中',
        4: '已完成'
      }, // 类型
      percentage: {
        0: 0,
        1: 25,
        2: 50,
        3: 75,
        4: 100
      },
      moreIndex: 0
    });
    const tableKey = ref(0);
    const getRadioType = () => {
      getDictionary('YPJDBBLX').then(res => {
        if (res) {
          state.statusList = [];
          res.data.data.dictionaryoption.forEach(val => {
            if (val.status === 1) {
              state.statusList[val.code] = val.name;
            }
          });
        }
      });
    };
    // 获取各类型数量
    const getTypeNumber = () => {
      getNumber({ mateCode: state.searchForm.mateCode, ...state.formInline, ...state.searchForm }).then(res => {
        // completeCount 已完成样品数量
        // experimentCount 检测中样品数量
        // reportCount 报告中样品数量
        // totalCount 全部样品数量l,
        // unAssignedCount 未分配样品数量
        // unInform 未下达样品数量
        const data = res.data.data;
        state.totalNumber = {
          '-1': data.totalCount,
          0: data.unInform,
          1: data.unAssignedCount,
          2: data.experimentCount,
          3: data.reportCount,
          4: data.completeCount
        };
      });
    };
    getTypeNumber();
    getRadioType();
    const changeStatus = val => {
      getTableList();
    };
    const reset = () => {
      state.formInline.key = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      state.searchForm = {
        mateCode: ''
      };
      getTableList();
      getTypeNumber();
    };

    const handleSelectionChange = val => {};

    // 树节点编辑
    const showEditDialog = ref(false);
    // 查询检测中的数据
    const checkRowNumber = row => {
      getRowNumber(row.sampleId).then(res => {
        state.rowNumberDetail = res.data.data;
      });
    };
    const clickArrow = () => {
      state.activeIndex = parseInt(state.activeIndex) + 1 + '';
    };
    const getTableList = query => {
      var params = {
        status: state.status.toString(),
        ...state.formInline,
        ...state.searchForm
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      state.tableLoading = true;
      getList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    getTableList();
    // 处理签发日期
    const handleSubmitTime = date => {
      state.searchForm.registerTime = formatDate(date);
    };
    // 查看详情
    const handleCheck = row => {
      state.detailDrawer = true;
      state.drawerLoading = true;
      getLine(row.sampleId).then(res => {
        state.drawerLoading = false;
        state.processList = [];
        const data = res.data.data.taskNode;
        data.forEach((item, index) => {
          if (item.endTime) {
            item.isHaved = true;
          } else if (item.reportData) {
            if (item.reportData.length > 0) {
              item.isHaved = true;
            }
          } else if (item.experimentData) {
            if (item.experimentData.length > 0) {
              item.isHaved = true;
            }
          } else if (index === data.length - 1 && item.isCurrentNode) {
            item.isHaved = true;
          }
          state.processList.push(item);
        });
      });
    };
    onMounted(() => {});
    // 高级搜索
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    const handleChangeMate = val => {
      state.searchForm.mateCode = val;
    };
    // 导出
    const handleExport = () => {
      const params = {
        status: state.status.toString(),
        mateCode: state.searchForm.mateCode,
        ...state.formInline,
        ...state.searchForm
      };
      getExport(params).then(res => {
        state.exportData = [];
        res.data.data.forEach((row, index) => {
          // 序号
          row.index = Number(index) + 1;
          // 样品进度
          row.statusName = state.statusList[row.status];
          // 检验对象
          row.jydx = row.type === 1 ? row.inputWarehouseNo : row.productionOrderNo;
          // 对象位置
          row.dxwz = row.type === 1 ? row.wareHouseName : row.productionProcedure + ' ' + row.productionStation;
          // 对象名称
          row.dxmc = row.type === 1 ? row.supplierName : row.customerName;
          // 试验负责人
          row.ownName = row.ownerId ? getNameByid(row.ownerId) : row.ownerId;
          state.exportData.push(row);
        });
        export2Excel();
        proxy.$message.success('导出成功！');
      });
    };
    const export2Excel = () => {
      state.expLoading = true;
      const fileName = '样品进度报表';
      const tHeader = [
        '序号',
        '样品编号',
        '样品进度',
        '样品名称',
        '规格型号',
        '物料编号',
        '批次',
        '盘号',
        '检验对象',
        '对象位置',
        '对象名称',
        '试验负责人'
      ];
      const filterVal = [
        'index',
        'secSampleNum',
        'statusName',
        'sampleName',
        'prodType',
        'materialNo',
        'batchNo',
        'reelNo',
        'jydx',
        'dxwz',
        'dxmc',
        'ownName'
      ];
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, state.exportData);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        state.expLoading = false;
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          return v[j];
        })
      );
    };
    // 样品、报告详情跳转
    const handleTz = (row, type) => {
      if (type === 'yp') {
        router.push({
          path: '/sample/schedule/detail',
          query: {
            orderId: row.orderId,
            sampleId: row.sampleId
          }
        });
      } else {
        router.push({
          path: '/sample/schedule/report/detail',
          query: {
            reportId: row.reportId,
            sampleId: row.sampleId,
            reportStage: 6
          }
        });
      }
    };
    return {
      ...toRefs(state),
      advancedSearch,
      handleSubmitTime,
      export2Excel,
      formatJson,
      handleChangeMate,
      getPermissionBtn,
      handleTz,
      getTypeNumber,
      handleExport,
      getRadioType,
      changeStatus,
      getTableList,
      formatDate,
      checkRowNumber,
      getNameByid,
      getNamesByid,
      drageHeader,
      showEditDialog,
      clickArrow,
      handleSelectionChange,
      handleCheck,
      tableKey,
      reset
    };
  }
};
</script>
<style lang="scss" scoped>
.timeLineContent {
  margin: 0 auto;
  max-height: calc(100vh - 90px);
  overflow-y: auto;
  padding: 40px 20px 20px 0;
}
.itemImage {
  vertical-align: text-bottom;
  margin-right: 5px;
}
:deep(.el-timeline) {
  padding-left: 20px;
}
.collapseTable {
  margin-top: 10px;
  .item {
    display: flex;
    align-items: center;
    .custom-icon {
      padding: 2px 5px;
      margin-right: 10px;
    }
  }
}
.hadEndTime {
  :deep(.el-timeline-item__node) {
    background: $tes-primary;
  }
}
.isCurrentNode {
  :deep(.el-timeline-item__node) {
    background: #e6a23c;
    animation-name: mymove;
    animation-duration: 1s;
    -webkit-animation-name: mymove;
    -webkit-animation-duration: 1s;
    animation-iteration-count: infinite;
  }
}
@keyframes mymove {
  0% {
    width: 14px;
    height: 14px;
    left: -2px;
    top: 0;
  }
  25% {
    width: 16px;
    height: 16px;
    left: -3px;
    top: -2px;
  }
  50% {
    width: 20px;
    height: 20px;
    left: -4px;
    top: -4px;
  }
  75% {
    width: 16px;
    height: 16px;
    left: -3px;
    top: -2px;
  }
  100% {
    width: 14px;
    height: 14px;
    left: -2px;
    top: 0;
  }
}

@-webkit-keyframes mymove {
  from {
    width: 14px;
    height: 14px;
  }
  to {
    width: 20px;
    height: 20px;
  }
}
.timeLineTop {
  line-height: 20px;
  text-align: left;
  .nodeName {
    font-size: 16px;
  }
  .endTime {
    font-size: 12px;
    color: $tes-font2;
    float: right;
  }
}

:deep(.el-collapse-item__header) {
  height: 20px;
  background-color: transparent;
  font-size: 16px;
  margin-bottom: 5px;
  border: 0;
}
:deep(.el-collapse) {
  border: 0;
}
:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}
:deep(.el-collapse-item__wrap) {
  border-bottom: 0;
}
.inlineBlock {
  display: inline-block;
}
.isShowHand {
  cursor: pointer;
}

.el-button--primary {
  border: 0;
  border-radius: 4px;
}
.searchLeft {
  width: 70%;
  display: inline-block;
  .el-input {
    margin-right: 8px;
    width: 38%;
  }
}
.el-menu--horizontal > .el-menu-item.is-active {
  color: $tes-primary;
  border-bottom: 2.25px solid $tes-primary;
}
:deep(.el-drawer__close-btn) {
  margin-right: 9px;
}
.searchRight {
  margin-right: 0;
}
</style>
<style lang="scss">
.schedulePopover,
.scheduleDrawer {
  .icon-tes-info:before,
  .icon-tes-success:before,
  .icon-tes-wait:before,
  .icon-tes-error:before,
  .icon-tes-blue:before,
  .icon-tes-green:before {
    display: inline-block;
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 4px;
    background: #e6a23c;
    margin-right: 8px;
    margin-bottom: 1px;
  }
  .icon-tes-success:before {
    background: #67c23a;
  }
  .icon-tes-green:before {
    background: $tes-primary;
  }
}

.scheduleDrawer .el-drawer__body {
  padding: 20px !important;
}
</style>
